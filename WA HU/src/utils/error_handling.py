"""
Error handling utilities for the Wiz Aroma Food Delivery system.
"""

import logging
import traceback
import functools
import telebot
from typing import Callable, Any, Optional, Dict, Union

# Get the logger
logger = logging.getLogger(__name__)


def handle_exceptions(
    func: Callable = None,
    error_message: str = "An error occurred",
    notify_user: bool = True,
    log_level: int = logging.ERROR,
    reraise: bool = False,
):
    """
    Decorator to handle exceptions in bot handlers.
    
    Args:
        func: The function to decorate
        error_message: Message to show to the user on error
        notify_user: Whether to notify the user about the error
        log_level: Logging level for the error
        reraise: Whether to re-raise the exception after handling
    
    Returns:
        Decorated function
    """
    def decorator(func):
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except Exception as e:
                # Get the traceback
                tb = traceback.format_exc()
                
                # Log the error with traceback
                logger.log(log_level, f"Error in {func.__name__}: {str(e)}\n{tb}")
                
                # Notify user if requested
                if notify_user:
                    # Try to extract message object and bot instance
                    message = None
                    bot_instance = None
                    
                    # Look for message in args
                    for arg in args:
                        if hasattr(arg, 'chat') and hasattr(arg, 'from_user'):
                            message = arg
                            break
                    
                    # Look for bot instance in args or use the first arg if it's a bot
                    if args and isinstance(args[0], telebot.TeleBot):
                        bot_instance = args[0]
                    
                    # If we have both message and bot, send error notification
                    if message and bot_instance:
                        try:
                            # Create a keyboard with main menu button
                            markup = telebot.types.ReplyKeyboardMarkup(row_width=1, resize_keyboard=True)
                            markup.add(telebot.types.KeyboardButton("🔙 Back to Main Menu"))
                            
                            # Send error message
                            bot_instance.send_message(
                                message.chat.id,
                                f"❌ {error_message}. Please try again or contact support.",
                                reply_markup=markup
                            )
                        except Exception as send_error:
                            logger.error(f"Failed to send error message: {str(send_error)}")
                
                # Re-raise if requested
                if reraise:
                    raise
                
                # Return None if not re-raising
                return None
        
        return wrapper
    
    # Handle case where decorator is used without arguments
    if func is not None:
        return decorator(func)
    
    return decorator


def safe_api_call(
    func: Callable,
    *args,
    max_retries: int = 3,
    retry_delay: float = 1.0,
    **kwargs
) -> Any:
    """
    Safely make API calls with retry logic.
    
    Args:
        func: The function to call
        args: Arguments to pass to the function
        max_retries: Maximum number of retries
        retry_delay: Delay between retries in seconds
        kwargs: Keyword arguments to pass to the function
    
    Returns:
        Result of the function call or None on failure
    """
    import time
    
    retries = 0
    last_error = None
    
    while retries < max_retries:
        try:
            return func(*args, **kwargs)
        except telebot.apihelper.ApiException as e:
            last_error = e
            logger.warning(f"API error in {func.__name__}: {str(e)}. Retry {retries+1}/{max_retries}")
            
            # Check if it's a rate limit error
            if "429" in str(e):
                # Extract retry_after if available
                retry_after = 5  # Default to 5 seconds
                if hasattr(e, 'result') and isinstance(e.result, dict) and 'parameters' in e.result:
                    retry_after = e.result['parameters'].get('retry_after', 5)
                
                logger.warning(f"Rate limited. Waiting {retry_after} seconds")
                time.sleep(retry_after)
            else:
                # For other errors, use the standard delay
                time.sleep(retry_delay)
            
            retries += 1
        except Exception as e:
            logger.error(f"Unexpected error in {func.__name__}: {str(e)}")
            return None
    
    # Log the final failure
    if last_error:
        logger.error(f"Failed after {max_retries} retries: {str(last_error)}")
    
    return None


def validate_data(data: Any, data_type: type, default_value: Any = None) -> Any:
    """
    Validate data type and return default if invalid.
    
    Args:
        data: Data to validate
        data_type: Expected type
        default_value: Default value to return if validation fails
    
    Returns:
        Validated data or default value
    """
    if isinstance(data, data_type):
        return data
    
    logger.warning(f"Data validation failed. Expected {data_type.__name__}, got {type(data).__name__}")
    return default_value
