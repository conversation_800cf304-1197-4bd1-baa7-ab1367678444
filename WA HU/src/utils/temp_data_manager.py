"""
Temporary Data Management for Wiz-Aroma System
Manages temporary data during order processing with automatic cleanup.
All persistent data is stored exclusively in Firebase Firestore.
"""

import time
import threading
from typing import Dict, Any, Optional, Set
from datetime import datetime, timedelta
import logging

from src.config import logger

# Configuration constants for temporary data management
TEMP_DATA_CLEANUP_INTERVAL = 300  # 5 minutes
ORDER_COMPLETION_CLEANUP_DELAY = 60  # 1 minute after order completion
MAX_TEMP_DATA_AGE = 3600  # 1 hour maximum age for any temporary data
USER_SESSION_TIMEOUT = 1800  # 30 minutes for user sessions

class TemporaryDataManager:
    """
    Manages temporary data during order processing with automatic cleanup.
    Ensures no data persists locally after order completion.
    """
    
    def __init__(self):
        self._temp_data: Dict[str, Dict[str, Any]] = {}
        self._data_timestamps: Dict[str, float] = {}
        self._cleanup_thread: Optional[threading.Thread] = None
        self._cleanup_running = False
        self._lock = threading.Lock()
        
    def start_cleanup_service(self):
        """Start the automatic cleanup service"""
        if self._cleanup_running:
            return
            
        self._cleanup_running = True
        self._cleanup_thread = threading.Thread(target=self._cleanup_loop, daemon=True)
        self._cleanup_thread.start()
        logger.info("Temporary data cleanup service started")
        
    def stop_cleanup_service(self):
        """Stop the automatic cleanup service"""
        self._cleanup_running = False
        if self._cleanup_thread:
            self._cleanup_thread.join(timeout=5)
        logger.info("Temporary data cleanup service stopped")
        
    def store_temp_data(self, key: str, data: Any, category: str = "general") -> bool:
        """
        Store temporary data with automatic cleanup tracking.
        
        Args:
            key: Unique identifier for the data
            data: Data to store temporarily
            category: Category of data (order, user_session, etc.)
            
        Returns:
            True if stored successfully
        """
        try:
            with self._lock:
                full_key = f"{category}:{key}"
                self._temp_data[full_key] = {
                    'data': data,
                    'category': category,
                    'created_at': time.time(),
                    'last_accessed': time.time()
                }
                self._data_timestamps[full_key] = time.time()
                
            logger.debug(f"Stored temporary data: {full_key}")
            return True
            
        except Exception as e:
            logger.error(f"Error storing temporary data {key}: {e}")
            return False
            
    def get_temp_data(self, key: str, category: str = "general") -> Optional[Any]:
        """
        Retrieve temporary data and update access timestamp.
        
        Args:
            key: Unique identifier for the data
            category: Category of data
            
        Returns:
            Stored data or None if not found
        """
        try:
            with self._lock:
                full_key = f"{category}:{key}"
                if full_key in self._temp_data:
                    self._temp_data[full_key]['last_accessed'] = time.time()
                    return self._temp_data[full_key]['data']
                return None
                
        except Exception as e:
            logger.error(f"Error retrieving temporary data {key}: {e}")
            return None
            
    def remove_temp_data(self, key: str, category: str = "general") -> bool:
        """
        Remove specific temporary data.
        
        Args:
            key: Unique identifier for the data
            category: Category of data
            
        Returns:
            True if removed successfully
        """
        try:
            with self._lock:
                full_key = f"{category}:{key}"
                if full_key in self._temp_data:
                    del self._temp_data[full_key]
                    del self._data_timestamps[full_key]
                    logger.debug(f"Removed temporary data: {full_key}")
                    return True
                return False
                
        except Exception as e:
            logger.error(f"Error removing temporary data {key}: {e}")
            return False
            
    def cleanup_order_data(self, order_number: str, delay_seconds: int = None) -> bool:
        """
        Clean up all temporary data related to a specific order.
        
        Args:
            order_number: Order number to clean up
            delay_seconds: Optional delay before cleanup (default from config)
            
        Returns:
            True if cleanup was successful
        """
        if delay_seconds is None:
            delay_seconds = ORDER_COMPLETION_CLEANUP_DELAY
            
        def delayed_cleanup():
            time.sleep(delay_seconds)
            try:
                with self._lock:
                    keys_to_remove = []
                    for full_key in self._temp_data.keys():
                        if order_number in full_key or f"order:{order_number}" == full_key:
                            keys_to_remove.append(full_key)
                    
                    for key in keys_to_remove:
                        del self._temp_data[key]
                        del self._data_timestamps[key]
                        
                    if keys_to_remove:
                        logger.info(f"Cleaned up {len(keys_to_remove)} temporary data entries for order {order_number}")
                        
            except Exception as e:
                logger.error(f"Error in delayed cleanup for order {order_number}: {e}")
                
        # Start cleanup in background thread
        cleanup_thread = threading.Thread(target=delayed_cleanup, daemon=True)
        cleanup_thread.start()
        return True
        
    def cleanup_expired_data(self) -> int:
        """
        Clean up expired temporary data.
        
        Returns:
            Number of items cleaned up
        """
        try:
            current_time = time.time()
            expired_keys = []
            
            with self._lock:
                for full_key, timestamp in self._data_timestamps.items():
                    if current_time - timestamp > MAX_TEMP_DATA_AGE:
                        expired_keys.append(full_key)
                
                for key in expired_keys:
                    if key in self._temp_data:
                        del self._temp_data[key]
                    if key in self._data_timestamps:
                        del self._data_timestamps[key]
                        
            if expired_keys:
                logger.info(f"Cleaned up {len(expired_keys)} expired temporary data entries")
                
            return len(expired_keys)
            
        except Exception as e:
            logger.error(f"Error cleaning up expired data: {e}")
            return 0
            
    def get_temp_data_stats(self) -> Dict[str, Any]:
        """
        Get statistics about temporary data storage.
        
        Returns:
            Dictionary with storage statistics
        """
        try:
            with self._lock:
                total_items = len(self._temp_data)
                categories = {}
                oldest_timestamp = None
                newest_timestamp = None
                
                for full_key, data in self._temp_data.items():
                    category = data['category']
                    categories[category] = categories.get(category, 0) + 1
                    
                    created_at = data['created_at']
                    if oldest_timestamp is None or created_at < oldest_timestamp:
                        oldest_timestamp = created_at
                    if newest_timestamp is None or created_at > newest_timestamp:
                        newest_timestamp = created_at
                
                return {
                    'total_items': total_items,
                    'categories': categories,
                    'oldest_item_age': time.time() - oldest_timestamp if oldest_timestamp else 0,
                    'newest_item_age': time.time() - newest_timestamp if newest_timestamp else 0,
                    'cleanup_running': self._cleanup_running
                }
                
        except Exception as e:
            logger.error(f"Error getting temp data stats: {e}")
            return {'error': str(e)}
            
    def _cleanup_loop(self):
        """Background cleanup loop"""
        while self._cleanup_running:
            try:
                self.cleanup_expired_data()
                time.sleep(TEMP_DATA_CLEANUP_INTERVAL)
            except Exception as e:
                logger.error(f"Error in cleanup loop: {e}")
                time.sleep(TEMP_DATA_CLEANUP_INTERVAL)
                
    def force_cleanup_all(self) -> int:
        """
        Force cleanup of all temporary data (emergency use only).
        
        Returns:
            Number of items cleaned up
        """
        try:
            with self._lock:
                count = len(self._temp_data)
                self._temp_data.clear()
                self._data_timestamps.clear()
                
            logger.warning(f"FORCE CLEANUP: Removed all {count} temporary data entries")
            return count
            
        except Exception as e:
            logger.error(f"Error in force cleanup: {e}")
            return 0


# Global instance for the application
temp_data_manager = TemporaryDataManager()


def initialize_temp_data_manager():
    """Initialize and start the temporary data manager"""
    temp_data_manager.start_cleanup_service()
    logger.info("Temporary data manager initialized")


def cleanup_business_data_after_order_completion(order_number: str, user_id: int):
    """
    Clean up all temporary business data after order completion.
    This ensures no business data persists locally after order lifecycle ends.

    Args:
        order_number: Order number that was completed
        user_id: User ID associated with the order
    """
    try:
        from src.data_models import (
            temp_orders, temp_order_status, temp_pending_admin_reviews,
            temp_admin_remarks, temp_awaiting_receipt, temp_delivery_locations,
            temp_current_order_numbers, temp_delivery_personnel_assignments,
            temp_delivery_personnel_availability, temp_delivery_personnel_capacity
        )

        user_id_str = str(user_id)
        cleanup_count = 0

        # Clean up user-specific temporary data
        if user_id_str in temp_orders:
            del temp_orders[user_id_str]
            cleanup_count += 1

        if user_id_str in temp_order_status:
            del temp_order_status[user_id_str]
            cleanup_count += 1

        if user_id_str in temp_delivery_locations:
            del temp_delivery_locations[user_id_str]
            cleanup_count += 1

        if user_id_str in temp_current_order_numbers:
            del temp_current_order_numbers[user_id_str]
            cleanup_count += 1

        # Clean up order-specific temporary data
        if order_number in temp_pending_admin_reviews:
            del temp_pending_admin_reviews[order_number]
            cleanup_count += 1

        if order_number in temp_admin_remarks:
            del temp_admin_remarks[order_number]
            cleanup_count += 1

        if order_number in temp_awaiting_receipt:
            del temp_awaiting_receipt[order_number]
            cleanup_count += 1

        # Clean up delivery assignments related to this order
        assignments_to_remove = []
        for assignment_id, assignment_data in temp_delivery_personnel_assignments.items():
            if assignment_data.get('order_number') == order_number:
                assignments_to_remove.append(assignment_id)

        for assignment_id in assignments_to_remove:
            del temp_delivery_personnel_assignments[assignment_id]
            cleanup_count += 1

        # Use the temporary data manager for additional cleanup
        temp_data_manager.cleanup_order_data(order_number, delay_seconds=ORDER_COMPLETION_CLEANUP_DELAY)

        logger.info(f"Cleaned up {cleanup_count} temporary business data entries for order {order_number}")
        return True

    except Exception as e:
        logger.error(f"Error cleaning up business data for order {order_number}: {e}")
        return False


def cleanup_user_session_data(user_id: int):
    """
    Clean up temporary user session data.
    Called when user session expires or user completes/cancels order.

    Args:
        user_id: User ID to clean up session data for
    """
    try:
        from src.data_models import (
            temp_orders, temp_order_status, temp_delivery_locations,
            temp_current_order_numbers
        )

        user_id_str = str(user_id)
        cleanup_count = 0

        # Clean up user session data
        session_data_keys = [
            (temp_orders, "temp_orders"),
            (temp_order_status, "temp_order_status"),
            (temp_delivery_locations, "temp_delivery_locations"),
            (temp_current_order_numbers, "temp_current_order_numbers")
        ]

        for data_dict, dict_name in session_data_keys:
            if user_id_str in data_dict:
                del data_dict[user_id_str]
                cleanup_count += 1
                logger.debug(f"Cleaned up {dict_name} for user {user_id}")

        # Use the temporary data manager for additional cleanup
        try:
            temp_data_manager.cleanup_expired_data()
        except AttributeError:
            # Method might not exist in current implementation
            logger.debug("temp_data_manager.cleanup_expired_data() not available")

        logger.info(f"Cleaned up {cleanup_count} user session data entries for user {user_id}")
        return True

    except Exception as e:
        logger.error(f"Error cleaning up user session data for user {user_id}: {e}")
        return False


def force_cleanup_all_temporary_business_data():
    """
    Force cleanup of all temporary business data.
    Emergency function to ensure no business data persists locally.
    """
    try:
        from src.data_models import (
            temp_orders, temp_order_status, temp_pending_admin_reviews,
            temp_admin_remarks, temp_awaiting_receipt, temp_delivery_locations,
            temp_current_order_numbers, temp_delivery_personnel_assignments,
            temp_delivery_personnel_availability, temp_delivery_personnel_capacity
        )

        # Clear all temporary business data dictionaries
        temp_data_dicts = [
            (temp_orders, "temp_orders"),
            (temp_order_status, "temp_order_status"),
            (temp_pending_admin_reviews, "temp_pending_admin_reviews"),
            (temp_admin_remarks, "temp_admin_remarks"),
            (temp_awaiting_receipt, "temp_awaiting_receipt"),
            (temp_delivery_locations, "temp_delivery_locations"),
            (temp_current_order_numbers, "temp_current_order_numbers"),
            (temp_delivery_personnel_assignments, "temp_delivery_personnel_assignments"),
            (temp_delivery_personnel_availability, "temp_delivery_personnel_availability"),
            (temp_delivery_personnel_capacity, "temp_delivery_personnel_capacity")
        ]

        total_cleaned = 0
        for data_dict, dict_name in temp_data_dicts:
            count = len(data_dict)
            data_dict.clear()
            total_cleaned += count
            logger.info(f"Cleared {count} entries from {dict_name}")

        # Force cleanup of temporary data manager
        manager_cleaned = temp_data_manager.force_cleanup_all()
        total_cleaned += manager_cleaned

        logger.warning(f"FORCE CLEANUP: Removed {total_cleaned} total temporary business data entries")
        return total_cleaned

    except Exception as e:
        logger.error(f"Error in force cleanup of temporary business data: {e}")
        return 0


def cleanup_temp_data_manager():
    """Clean up and stop the temporary data manager"""
    temp_data_manager.stop_cleanup_service()
    logger.info("Temporary data manager cleaned up")
