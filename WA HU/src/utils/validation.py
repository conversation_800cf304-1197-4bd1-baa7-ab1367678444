"""
Enhanced validation utility functions for the Wiz Aroma Food Delivery system.
Includes security-focused input validation and sanitization.
"""

import re
import html
import logging
from typing import Optional, Union, List, Dict, Any

logger = logging.getLogger(__name__)

# Security patterns for input validation
DANGEROUS_PATTERNS = [
    r'<script[^>]*>.*?</script>',  # Script tags
    r'javascript:',               # JavaScript URLs
    r'on\w+\s*=',                # Event handlers
    r'<iframe[^>]*>.*?</iframe>', # Iframes
    r'<object[^>]*>.*?</object>', # Objects
    r'<embed[^>]*>.*?</embed>',   # Embeds
    r'<link[^>]*>',               # Link tags
    r'<meta[^>]*>',               # Meta tags
    r'<style[^>]*>.*?</style>',   # Style tags
    r'data:text/html',            # Data URLs
    r'vbscript:',                 # VBScript URLs
]

# Path traversal patterns
PATH_TRAVERSAL_PATTERNS = [
    r'\.\.',                      # Directory traversal
    r'\/\.\.',                    # Unix path traversal
    r'\\\.\.',                    # Windows path traversal
    r'%2e%2e',                    # URL encoded ..
    r'%252e%252e',                # Double URL encoded ..
    r'\.\/\.',                    # Current directory traversal
]

# SQL/NoSQL injection patterns
INJECTION_PATTERNS = [
    r'(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION)\b)',
    r'(\$where|\$ne|\$gt|\$lt|\$gte|\$lte|\$in|\$nin|\$regex)',  # MongoDB operators
    r'(;|\||&|\$\(|\`)',          # Command injection
    r'(<|>|\'|"|%|\\)',           # Basic injection chars
]


def sanitize_input(input_str: str, max_length: int = 1000) -> str:
    """
    Sanitize user input to prevent XSS and injection attacks.

    Args:
        input_str: The input string to sanitize
        max_length: Maximum allowed length

    Returns:
        Sanitized string
    """
    if not isinstance(input_str, str):
        return str(input_str)[:max_length]

    # Truncate to max length
    sanitized = input_str[:max_length]

    # HTML encode to prevent XSS
    sanitized = html.escape(sanitized)

    # Remove null bytes
    sanitized = sanitized.replace('\x00', '')

    # Remove control characters except newline and tab
    sanitized = ''.join(char for char in sanitized if ord(char) >= 32 or char in '\n\t')

    return sanitized.strip()


def validate_against_patterns(input_str: str, patterns: List[str], pattern_name: str) -> bool:
    """
    Validate input against dangerous patterns.

    Args:
        input_str: Input to validate
        patterns: List of regex patterns to check
        pattern_name: Name of pattern type for logging

    Returns:
        True if input is safe, False if dangerous patterns found
    """
    if not isinstance(input_str, str):
        return True

    for pattern in patterns:
        if re.search(pattern, input_str, re.IGNORECASE):
            logger.warning(f"Dangerous {pattern_name} pattern detected: {pattern}")
            return False

    return True


def is_safe_input(input_str: str) -> bool:
    """
    Comprehensive safety check for user input.

    Args:
        input_str: Input to validate

    Returns:
        True if input is safe, False otherwise
    """
    if not isinstance(input_str, str):
        return True

    # Check for XSS patterns
    if not validate_against_patterns(input_str, DANGEROUS_PATTERNS, "XSS"):
        return False

    # Check for path traversal
    if not validate_against_patterns(input_str, PATH_TRAVERSAL_PATTERNS, "path traversal"):
        return False

    # Check for injection patterns
    if not validate_against_patterns(input_str, INJECTION_PATTERNS, "injection"):
        return False

    return True


def validate_telegram_id(telegram_id: Union[str, int]) -> Optional[int]:
    """
    Validate and convert Telegram ID to integer.

    Args:
        telegram_id: Telegram ID as string or int

    Returns:
        Valid integer ID or None if invalid
    """
    try:
        tid = int(telegram_id)
        # Telegram IDs are typically 9-10 digits
        if 100000000 <= tid <= 9999999999:
            return tid
        else:
            logger.warning(f"Invalid Telegram ID range: {tid}")
            return None
    except (ValueError, TypeError):
        logger.warning(f"Invalid Telegram ID format: {telegram_id}")
        return None


def validate_chat_ids_list(chat_ids: List[Union[str, int]]) -> List[int]:
    """
    Validate a list of chat IDs and return only valid ones.

    Args:
        chat_ids: List of chat IDs to validate

    Returns:
        List of valid integer chat IDs
    """
    valid_ids = []
    for chat_id in chat_ids:
        validated_id = validate_telegram_id(chat_id)
        if validated_id is not None:
            valid_ids.append(validated_id)
        else:
            logger.warning(f"Skipping invalid chat ID: {chat_id}")
    return valid_ids


def test_telegram_chat_accessibility(bot_instance, chat_id: int) -> bool:
    """
    Test if a chat ID is accessible by attempting to get chat info.

    Args:
        bot_instance: Telegram bot instance
        chat_id: Chat ID to test

    Returns:
        True if chat is accessible, False otherwise
    """
    try:
        bot_instance.get_chat(chat_id)
        return True
    except Exception as e:
        logger.warning(f"Chat {chat_id} not accessible: {e}")
        return False


def is_valid_phone(phone: str) -> bool:
    """
    Validate phone number format with enhanced security.

    Args:
        phone: Phone number to validate

    Returns:
        True if valid phone format
    """
    if not isinstance(phone, str):
        return False

    # Sanitize input first
    phone = sanitize_input(phone, max_length=20)

    # Check if input is safe
    if not is_safe_input(phone):
        return False

    patterns = [
        r"^09\d{8}$",  # 09xxxxxxxx
        r"^07\d{8}$",  # 07xxxxxxxx
        r"^\+2519\d{8}$",  # +2519xxxxxxxx
        r"^\+2517\d{8}$",  # +2517xxxxxxxx
    ]
    return any(re.match(pattern, phone) for pattern in patterns)


def validate_name(name: str) -> bool:
    """
    Validate personnel/user name with security checks.

    Args:
        name: Name to validate

    Returns:
        True if valid name
    """
    if not isinstance(name, str):
        return False

    # Sanitize input
    name = sanitize_input(name, max_length=100)

    # Check if input is safe
    if not is_safe_input(name):
        return False

    # Check length
    if len(name.strip()) < 2 or len(name.strip()) > 50:
        return False

    # Allow letters, numbers, spaces, and common punctuation
    allowed_pattern = r'^[a-zA-Z0-9\s\-\'\.,]+$'
    return bool(re.match(allowed_pattern, name))


def validate_firebase_path(path: str) -> bool:
    """
    Validate Firebase database path to prevent injection.

    Args:
        path: Firebase path to validate

    Returns:
        True if path is safe
    """
    if not isinstance(path, str):
        return False

    # Check for path traversal
    if not validate_against_patterns(path, PATH_TRAVERSAL_PATTERNS, "path traversal"):
        return False

    # Firebase paths should only contain alphanumeric, underscore, hyphen, slash
    safe_pattern = r'^[a-zA-Z0-9_\-/]+$'
    return bool(re.match(safe_pattern, path))


def calculate_points(delivery_fee: Union[int, float]) -> int:
    """
    Calculate points as 11% of delivery fee, minimum 1 point.
    The decimal part is truncated (not rounded) as per business rules.
    Displayed to users as (10 + 1)% for marketing appeal.

    Args:
        delivery_fee: Delivery fee amount

    Returns:
        Calculated points (minimum 1)
    """
    try:
        fee = float(delivery_fee)
        if fee < 0:
            return 1
        points = max(1, int(fee * 0.11))
        return points
    except (ValueError, TypeError):
        logger.warning(f"Invalid delivery fee for points calculation: {delivery_fee}")
        return 1
