"""
Time utility functions for the Wiz Aroma Food Delivery system.
"""

import datetime
import logging


def is_open_now() -> bool:
    """Check if the bot is currently open based on working hours."""
    try:
        # Properly get Ethiopian local time (EAT, UTC+3)
        # Get UTC time using the recommended non-deprecated approach
        utc_now = datetime.datetime.now(datetime.timezone.utc)

        # Add 3 hours to get Ethiopian time
        ethiopia_offset = datetime.timedelta(hours=3)
        ethiopia_now = utc_now + ethiopia_offset

        current_time = ethiopia_now.time()
        current_day = ethiopia_now.weekday()  # Monday is 0 and Sunday is 6

        # For debugging
        logging.info(f"UTC time: {utc_now}, Ethiopian time: {ethiopia_now}")
        logging.info(
            f"Current day in Ethiopia: {current_day}, Current time in Ethiopia: {current_time}"
        )

        # Define working hours in Ethiopian local time
        if 0 <= current_day <= 4:  # Monday to Friday (0-4)
            morning_start = datetime.time(00, 00)
            morning_end = datetime.time(13, 30)
            afternoon_start = datetime.time(13, 31)
            afternoon_end = datetime.time(23, 59)

            # Check if current time is within working hours
            if (morning_start <= current_time <= morning_end) or (
                afternoon_start <= current_time <= afternoon_end
            ):
                logging.info(
                    f"Within working hours (weekday). Day: {current_day}, Time: {current_time}"
                )
                return True

        elif current_day in [5, 6]:  # Saturday and Sunday
            morning_start = datetime.time(00, 30)
            morning_end = datetime.time(23, 59)

            # Check if current time is within working hours
            if morning_start <= current_time <= morning_end:
                logging.info(
                    f"Within working hours (weekend). Day: {current_day}, Time: {current_time}"
                )
                return True

        return False
    except Exception as e:
        logging.error(f"Error in is_open_now function: {e}")
        # In case of an error, default to open to avoid blocking users
        return True


def generate_order_number(user_id, order_count=None):
    """Generate unique order number in format USERID_YYMMDDHHMM_XXXX
    where:
    - USERID is the Telegram user ID
    - YYMMDDHHMM is the date and time (2-digit year, month, day, hour, minute)
    - XXXX is the incremental count for that user"""
    from src.data_models import user_order_counts

    # Get or initialize user's order count
    if order_count is None:
        user_order_counts[user_id] = user_order_counts.get(user_id, 0) + 1
        order_count = user_order_counts[user_id]

    # Get Ethiopian local time (EAT, UTC+3)
    utc_now = datetime.datetime.now(datetime.timezone.utc)
    ethiopia_offset = datetime.timedelta(hours=3)
    ethiopia_now = utc_now + ethiopia_offset

    # Format the current time in Ethiopian time
    date_time_str = ethiopia_now.strftime("%y%m%d%H%M")  # Using Ethiopian time

    # Format the order count to 4 digits
    count_str = str(order_count).zfill(4)

    # Combine all parts with user_id
    return f"{user_id}_{date_time_str}_{count_str}"
