"""
Enhanced access control utilities for the Wiz Aroma Food Delivery system.
Provides secure authorization and rate limiting functionality.
"""

import time
import logging
from typing import Dict, List, Optional, Set
from collections import defaultdict, deque
from functools import wraps
from datetime import datetime, timedelta

logger = logging.getLogger(__name__)

# Rate limiting configuration
RATE_LIMITS = {
    'default': {'requests': 10, 'window': 60},  # 10 requests per minute
    'admin': {'requests': 50, 'window': 60},    # 50 requests per minute for admins
    'sensitive': {'requests': 3, 'window': 300}, # 3 requests per 5 minutes for sensitive ops
}

# Track user actions for rate limiting
user_action_times: Dict[int, Dict[str, deque]] = defaultdict(lambda: defaultdict(lambda: deque(maxlen=100)))

# Failed authentication attempts tracking
failed_auth_attempts: Dict[int, List[float]] = defaultdict(list)
MAX_FAILED_ATTEMPTS = 5
LOCKOUT_DURATION = 300  # 5 minutes

# Session management
active_sessions: Dict[int, Dict[str, any]] = {}
SESSION_TIMEOUT = 3600  # 1 hour


class AccessDeniedError(Exception):
    """Raised when access is denied"""
    pass


class RateLimitExceededError(Exception):
    """Raised when rate limit is exceeded"""
    pass


def validate_telegram_id(telegram_id) -> Optional[int]:
    """
    Validate and convert Telegram ID with enhanced security.
    
    Args:
        telegram_id: Telegram ID to validate
        
    Returns:
        Valid integer ID or None if invalid
    """
    from src.utils.validation import validate_telegram_id
    return validate_telegram_id(telegram_id)


def is_user_locked_out(user_id: int) -> bool:
    """
    Check if user is locked out due to failed authentication attempts.
    
    Args:
        user_id: User ID to check
        
    Returns:
        True if user is locked out
    """
    if user_id not in failed_auth_attempts:
        return False
    
    current_time = time.time()
    attempts = failed_auth_attempts[user_id]
    
    # Remove old attempts outside lockout window
    attempts[:] = [attempt for attempt in attempts if current_time - attempt < LOCKOUT_DURATION]
    
    return len(attempts) >= MAX_FAILED_ATTEMPTS


def record_failed_auth(user_id: int):
    """
    Record a failed authentication attempt.
    
    Args:
        user_id: User ID that failed authentication
    """
    current_time = time.time()
    failed_auth_attempts[user_id].append(current_time)
    
    # Keep only recent attempts
    attempts = failed_auth_attempts[user_id]
    attempts[:] = [attempt for attempt in attempts if current_time - attempt < LOCKOUT_DURATION]
    
    logger.warning(f"Failed authentication attempt for user {user_id}. Total recent attempts: {len(attempts)}")


def clear_failed_auth(user_id: int):
    """
    Clear failed authentication attempts for a user.
    
    Args:
        user_id: User ID to clear attempts for
    """
    if user_id in failed_auth_attempts:
        del failed_auth_attempts[user_id]


def check_rate_limit(user_id: int, action_type: str = 'default') -> bool:
    """
    Check if user has exceeded rate limit for specific action type.
    
    Args:
        user_id: User ID to check
        action_type: Type of action (default, admin, sensitive)
        
    Returns:
        True if within rate limit, False if exceeded
    """
    if action_type not in RATE_LIMITS:
        action_type = 'default'
    
    limit_config = RATE_LIMITS[action_type]
    max_requests = limit_config['requests']
    window_seconds = limit_config['window']
    
    current_time = time.time()
    user_actions = user_action_times[user_id][action_type]
    
    # Remove old actions outside the window
    while user_actions and current_time - user_actions[0] > window_seconds:
        user_actions.popleft()
    
    # Check if limit exceeded
    if len(user_actions) >= max_requests:
        logger.warning(f"Rate limit exceeded for user {user_id}, action {action_type}: {len(user_actions)}/{max_requests}")
        return False
    
    # Record this action
    user_actions.append(current_time)
    return True


def rate_limit(action_type: str = 'default'):
    """
    Decorator to enforce rate limiting on functions.
    
    Args:
        action_type: Type of action for rate limiting
    """
    def decorator(func):
        @wraps(func)
        def wrapper(message, *args, **kwargs):
            user_id = message.from_user.id
            
            if not check_rate_limit(user_id, action_type):
                raise RateLimitExceededError(f"Rate limit exceeded for action: {action_type}")
            
            return func(message, *args, **kwargs)
        return wrapper
    return decorator


def get_authorized_admin_ids() -> List[int]:
    """
    Get list of authorized admin IDs from Firebase with fallback.
    
    Returns:
        List of authorized admin IDs
    """
    try:
        from src.firebase_db import get_data
        from src.config import ADMIN_CHAT_IDS
        
        # Try to get from Firebase first
        admin_config = get_data("system_config/authorized_admins")
        if admin_config and isinstance(admin_config, dict):
            firebase_admins = admin_config.get('admin_ids', [])
            if firebase_admins:
                return [validate_telegram_id(aid) for aid in firebase_admins if validate_telegram_id(aid)]
        
        # Fallback to config file
        return [validate_telegram_id(aid) for aid in ADMIN_CHAT_IDS if validate_telegram_id(aid)]
        
    except Exception as e:
        logger.error(f"Error getting authorized admin IDs: {e}")
        return []


def get_authorized_delivery_ids() -> List[int]:
    """
    Get list of authorized delivery personnel IDs from Firebase.
    
    Returns:
        List of authorized delivery personnel IDs
    """
    try:
        from src.firebase_db import get_data
        
        authorized_personnel = get_data("authorized_delivery_personnel") or {}
        authorized_ids = []
        
        for personnel_id, person_data in authorized_personnel.items():
            if isinstance(person_data, dict) and person_data.get('status') == 'active':
                telegram_id = validate_telegram_id(person_data.get('telegram_id'))
                if telegram_id:
                    authorized_ids.append(telegram_id)
        
        return authorized_ids
        
    except Exception as e:
        logger.error(f"Error getting authorized delivery IDs: {e}")
        return []


def is_admin(user_id: int) -> bool:
    """
    Check if user is an authorized admin with security checks.
    
    Args:
        user_id: User ID to check
        
    Returns:
        True if user is authorized admin
    """
    validated_id = validate_telegram_id(user_id)
    if not validated_id:
        return False
    
    if is_user_locked_out(validated_id):
        logger.warning(f"Admin access denied for locked out user: {validated_id}")
        return False
    
    authorized_admins = get_authorized_admin_ids()
    is_authorized = validated_id in authorized_admins
    
    if not is_authorized:
        record_failed_auth(validated_id)
        logger.warning(f"Unauthorized admin access attempt by user: {validated_id}")
    else:
        clear_failed_auth(validated_id)
        logger.info(f"Admin access granted to user: {validated_id}")
    
    return is_authorized


def is_delivery_personnel(user_id: int) -> bool:
    """
    Check if user is authorized delivery personnel with security checks.
    
    Args:
        user_id: User ID to check
        
    Returns:
        True if user is authorized delivery personnel
    """
    validated_id = validate_telegram_id(user_id)
    if not validated_id:
        return False
    
    if is_user_locked_out(validated_id):
        logger.warning(f"Delivery access denied for locked out user: {validated_id}")
        return False
    
    authorized_delivery = get_authorized_delivery_ids()
    is_authorized = validated_id in authorized_delivery
    
    if not is_authorized:
        record_failed_auth(validated_id)
        logger.warning(f"Unauthorized delivery access attempt by user: {validated_id}")
    else:
        clear_failed_auth(validated_id)
        logger.info(f"Delivery access granted to user: {validated_id}")
    
    return is_authorized


def require_admin(func):
    """
    Decorator to require admin authorization for function access.
    """
    @wraps(func)
    def wrapper(message, *args, **kwargs):
        user_id = message.from_user.id
        
        if not is_admin(user_id):
            raise AccessDeniedError("Admin authorization required")
        
        return func(message, *args, **kwargs)
    return wrapper


def require_delivery_personnel(func):
    """
    Decorator to require delivery personnel authorization for function access.
    """
    @wraps(func)
    def wrapper(message, *args, **kwargs):
        user_id = message.from_user.id
        
        if not is_delivery_personnel(user_id):
            raise AccessDeniedError("Delivery personnel authorization required")
        
        return func(message, *args, **kwargs)
    return wrapper


def create_session(user_id: int, session_data: Dict[str, any]) -> str:
    """
    Create a secure session for a user.
    
    Args:
        user_id: User ID
        session_data: Session data to store
        
    Returns:
        Session ID
    """
    import uuid
    
    session_id = str(uuid.uuid4())
    active_sessions[user_id] = {
        'session_id': session_id,
        'created_at': time.time(),
        'data': session_data
    }
    
    return session_id


def validate_session(user_id: int, session_id: str) -> bool:
    """
    Validate a user session.
    
    Args:
        user_id: User ID
        session_id: Session ID to validate
        
    Returns:
        True if session is valid
    """
    if user_id not in active_sessions:
        return False
    
    session = active_sessions[user_id]
    current_time = time.time()
    
    # Check if session expired
    if current_time - session['created_at'] > SESSION_TIMEOUT:
        del active_sessions[user_id]
        return False
    
    # Check session ID
    return session['session_id'] == session_id


def cleanup_expired_sessions():
    """
    Clean up expired sessions and old failed attempts.
    """
    current_time = time.time()
    
    # Clean up expired sessions
    expired_users = []
    for user_id, session in active_sessions.items():
        if current_time - session['created_at'] > SESSION_TIMEOUT:
            expired_users.append(user_id)
    
    for user_id in expired_users:
        del active_sessions[user_id]
    
    # Clean up old failed attempts
    for user_id in list(failed_auth_attempts.keys()):
        attempts = failed_auth_attempts[user_id]
        attempts[:] = [attempt for attempt in attempts if current_time - attempt < LOCKOUT_DURATION]
        if not attempts:
            del failed_auth_attempts[user_id]
    
    logger.debug(f"Cleaned up {len(expired_users)} expired sessions and old failed attempts")


# Schedule periodic cleanup
import threading
import atexit

def _periodic_cleanup():
    """Periodic cleanup of expired data"""
    while True:
        time.sleep(300)  # Run every 5 minutes
        try:
            cleanup_expired_sessions()
        except Exception as e:
            logger.error(f"Error in periodic cleanup: {e}")

# Start cleanup thread
cleanup_thread = threading.Thread(target=_periodic_cleanup, daemon=True)
cleanup_thread.start()

# Cleanup on exit
atexit.register(cleanup_expired_sessions)
