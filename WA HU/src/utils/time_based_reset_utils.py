"""
Time-Based Reset Utilities for Wiz Aroma Food Delivery Management System

Handles automatic time-based resets for incomplete order counts and analytics data:
- Daily resets at midnight
- Weekly resets every Monday
- Monthly resets on the 1st of each month

These resets only affect counting metrics, not actual order status in Firebase.
"""

import datetime
from typing import Dict, Any, Optional, Tuple
import logging

from src.config import logger
from src.firebase_db import get_data, set_data, update_data

# Reset tracking data structure
reset_tracking = {
    'last_daily_reset': None,
    'last_weekly_reset': None,
    'last_monthly_reset': None,
    'reset_counts': {
        'daily': 0,
        'weekly': 0,
        'monthly': 0
    }
}

def get_current_time_periods() -> Dict[str, Any]:
    """Get current time period identifiers"""
    now = datetime.datetime.now()
    
    return {
        'current_date': now.strftime('%Y-%m-%d'),
        'current_week_start': (now - datetime.timedelta(days=now.weekday())).strftime('%Y-%m-%d'),
        'current_month_start': now.replace(day=1).strftime('%Y-%m-%d'),
        'current_year': now.year,
        'current_month': now.month,
        'current_day': now.day,
        'current_weekday': now.weekday(),  # 0 = Monday
        'timestamp': now.isoformat()
    }

def should_reset_daily() -> bool:
    """Check if daily reset is needed (new day since last reset)"""
    try:
        tracking_data = get_data("time_based_reset_tracking") or {}
        last_daily_reset = tracking_data.get('last_daily_reset')
        
        if not last_daily_reset:
            return True
            
        current_periods = get_current_time_periods()
        current_date = current_periods['current_date']
        
        # Reset if it's a new day
        return last_daily_reset != current_date
        
    except Exception as e:
        logger.error(f"Error checking daily reset: {e}")
        return False

def should_reset_weekly() -> bool:
    """Check if weekly reset is needed (new week since last reset)"""
    try:
        tracking_data = get_data("time_based_reset_tracking") or {}
        last_weekly_reset = tracking_data.get('last_weekly_reset')
        
        if not last_weekly_reset:
            return True
            
        current_periods = get_current_time_periods()
        current_week_start = current_periods['current_week_start']
        
        # Reset if it's a new week (Monday)
        return last_weekly_reset != current_week_start
        
    except Exception as e:
        logger.error(f"Error checking weekly reset: {e}")
        return False

def should_reset_monthly() -> bool:
    """Check if monthly reset is needed (new month since last reset)"""
    try:
        tracking_data = get_data("time_based_reset_tracking") or {}
        last_monthly_reset = tracking_data.get('last_monthly_reset')
        
        if not last_monthly_reset:
            return True
            
        current_periods = get_current_time_periods()
        current_month_start = current_periods['current_month_start']
        
        # Reset if it's a new month
        return last_monthly_reset != current_month_start
        
    except Exception as e:
        logger.error(f"Error checking monthly reset: {e}")
        return False

def create_incomplete_count_snapshot() -> Dict[str, Any]:
    """Create a snapshot of current incomplete order counts for reset tracking"""
    try:
        from src.bots.management_bot import categorize_orders_by_status, calculate_category_percentages
        
        # Get current order data
        completed_orders = get_data("completed_orders") or {}
        confirmed_orders = get_data("confirmed_orders") or {}
        assignments_data = get_data("delivery_personnel_assignments") or {}
        
        # Categorize orders
        categorized_orders = categorize_orders_by_status(
            completed_orders, confirmed_orders, assignments_data
        )
        
        # Calculate statistics
        stats = calculate_category_percentages(categorized_orders)
        
        current_periods = get_current_time_periods()
        
        return {
            'timestamp': current_periods['timestamp'],
            'date': current_periods['current_date'],
            'week_start': current_periods['current_week_start'],
            'month_start': current_periods['current_month_start'],
            'incomplete_count': stats['incomplete_count'],
            'complete_count': stats['complete_count'],
            'issue_count': stats['issue_count'],
            'total_count': stats['total_count'],
            'incomplete_orders': [
                {
                    'order_id': order.get('order_number', 'unknown'),
                    'delivery_status': order.get('delivery_status', 'unknown'),
                    'confirmed_at': order.get('confirmed_at', ''),
                    'has_issue': order.get('delivery_status') == 'delivery_issue' or 
                               order.get('issue_reported_at') is not None
                }
                for order in categorized_orders['incomplete']
            ]
        }
        
    except Exception as e:
        logger.error(f"Error creating incomplete count snapshot: {e}")
        return {}

def execute_daily_reset() -> bool:
    """Execute daily reset of incomplete order counts"""
    try:
        logger.info("🔄 Executing daily reset of incomplete order counts")
        
        # Create snapshot before reset
        snapshot = create_incomplete_count_snapshot()
        
        current_periods = get_current_time_periods()
        
        # Update tracking data
        tracking_data = get_data("time_based_reset_tracking") or {}
        tracking_data.update({
            'last_daily_reset': current_periods['current_date'],
            'last_daily_reset_timestamp': current_periods['timestamp'],
            'daily_reset_count': tracking_data.get('daily_reset_count', 0) + 1
        })
        
        # Store the snapshot for historical tracking
        snapshot_id = f"daily_reset_{current_periods['current_date']}"
        set_data(f"reset_snapshots/{snapshot_id}", snapshot)
        
        # Update tracking
        success = set_data("time_based_reset_tracking", tracking_data)
        
        if success:
            logger.info(f"✅ Daily reset completed. Incomplete orders before reset: {snapshot.get('incomplete_count', 0)}")
        else:
            logger.error("❌ Failed to save daily reset tracking data")
            
        return success
        
    except Exception as e:
        logger.error(f"Error executing daily reset: {e}")
        return False

def execute_weekly_reset() -> bool:
    """Execute weekly reset of incomplete order counts"""
    try:
        logger.info("🔄 Executing weekly reset of incomplete order counts")
        
        # Create snapshot before reset
        snapshot = create_incomplete_count_snapshot()
        
        current_periods = get_current_time_periods()
        
        # Update tracking data
        tracking_data = get_data("time_based_reset_tracking") or {}
        tracking_data.update({
            'last_weekly_reset': current_periods['current_week_start'],
            'last_weekly_reset_timestamp': current_periods['timestamp'],
            'weekly_reset_count': tracking_data.get('weekly_reset_count', 0) + 1
        })
        
        # Store the snapshot for historical tracking
        snapshot_id = f"weekly_reset_{current_periods['current_week_start']}"
        set_data(f"reset_snapshots/{snapshot_id}", snapshot)
        
        # Update tracking
        success = set_data("time_based_reset_tracking", tracking_data)
        
        if success:
            logger.info(f"✅ Weekly reset completed. Incomplete orders before reset: {snapshot.get('incomplete_count', 0)}")
        else:
            logger.error("❌ Failed to save weekly reset tracking data")
            
        return success
        
    except Exception as e:
        logger.error(f"Error executing weekly reset: {e}")
        return False

def execute_monthly_reset() -> bool:
    """Execute monthly reset of incomplete order counts"""
    try:
        logger.info("🔄 Executing monthly reset of incomplete order counts")
        
        # Create snapshot before reset
        snapshot = create_incomplete_count_snapshot()
        
        current_periods = get_current_time_periods()
        
        # Update tracking data
        tracking_data = get_data("time_based_reset_tracking") or {}
        tracking_data.update({
            'last_monthly_reset': current_periods['current_month_start'],
            'last_monthly_reset_timestamp': current_periods['timestamp'],
            'monthly_reset_count': tracking_data.get('monthly_reset_count', 0) + 1
        })
        
        # Store the snapshot for historical tracking
        snapshot_id = f"monthly_reset_{current_periods['current_month_start']}"
        set_data(f"reset_snapshots/{snapshot_id}", snapshot)
        
        # Update tracking
        success = set_data("time_based_reset_tracking", tracking_data)
        
        if success:
            logger.info(f"✅ Monthly reset completed. Incomplete orders before reset: {snapshot.get('incomplete_count', 0)}")
        else:
            logger.error("❌ Failed to save monthly reset tracking data")
            
        return success
        
    except Exception as e:
        logger.error(f"Error executing monthly reset: {e}")
        return False

def check_and_execute_time_based_resets() -> Dict[str, bool]:
    """Check and execute all necessary time-based resets including analytics counters"""
    results = {
        'daily_reset': False,
        'weekly_reset': False,
        'monthly_reset': False,
        'analytics_counters_reset': False
    }

    try:
        # Check and execute analytics counter resets first
        try:
            from src.utils.analytics_counter_system import check_and_execute_counter_resets
            counter_resets = check_and_execute_counter_resets()
            if any(counter_resets.values()):
                results['analytics_counters_reset'] = True
                logger.info(f"🔄 Analytics counters reset: {counter_resets}")
        except ImportError:
            logger.warning("Analytics counter system not available")
        except Exception as e:
            logger.error(f"Error in analytics counter reset: {e}")

        # Check and execute daily reset
        if should_reset_daily():
            results['daily_reset'] = execute_daily_reset()

        # Check and execute weekly reset
        if should_reset_weekly():
            results['weekly_reset'] = execute_weekly_reset()

        # Check and execute monthly reset
        if should_reset_monthly():
            results['monthly_reset'] = execute_monthly_reset()

        # Log results
        executed_resets = [k for k, v in results.items() if v]
        if executed_resets:
            logger.info(f"🔄 Time-based resets executed: {', '.join(executed_resets)}")

        return results
        
    except Exception as e:
        logger.error(f"Error in check_and_execute_time_based_resets: {e}")
        return results

def get_reset_status() -> Dict[str, Any]:
    """Get current reset status and tracking information"""
    try:
        tracking_data = get_data("time_based_reset_tracking") or {}
        current_periods = get_current_time_periods()
        
        return {
            'current_periods': current_periods,
            'tracking_data': tracking_data,
            'needs_daily_reset': should_reset_daily(),
            'needs_weekly_reset': should_reset_weekly(),
            'needs_monthly_reset': should_reset_monthly(),
            'last_check_timestamp': datetime.datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Error getting reset status: {e}")
        return {}
