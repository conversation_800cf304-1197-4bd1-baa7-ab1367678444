"""
Data Management Patterns for Wiz-Aroma System
Establishes clear naming conventions and patterns to distinguish between:
- Temporary business data (temp_*)
- Persistent configuration data (config_*)
- Firebase operations (firebase_*)
"""

import logging
from typing import Dict, Any, Optional, List, Union
from enum import Enum

from src.config import logger


class DataCategory(Enum):
    """Categories of data in the Wiz-Aroma system"""
    TEMPORARY_BUSINESS = "temp"      # Temporary business data (cleared after order completion)
    PERSISTENT_USER = "persistent"   # Persistent user data (stored in Firebase)
    STATIC_CONFIG = "config"         # Static configuration data (cached locally)
    FIREBASE_OPERATION = "firebase"  # Firebase operations


class DataManagementPatterns:
    """
    Standardized patterns for data management in Wiz-Aroma system.
    Provides clear guidelines and utilities for different types of data.
    """
    
    # ========================================================================
    # NAMING CONVENTIONS
    # ========================================================================
    
    @staticmethod
    def get_temp_data_name(base_name: str) -> str:
        """
        Get standardized name for temporary business data.
        
        Args:
            base_name: Base name (e.g., 'orders', 'admin_reviews')
            
        Returns:
            Standardized temporary data name (e.g., 'temp_orders')
        """
        if base_name.startswith('temp_'):
            return base_name
        return f"temp_{base_name}"
    
    @staticmethod
    def get_config_data_name(base_name: str) -> str:
        """
        Get standardized name for configuration data.
        
        Args:
            base_name: Base name (e.g., 'areas', 'restaurants')
            
        Returns:
            Standardized config data name (e.g., 'config_areas')
        """
        if base_name.startswith('config_'):
            return base_name
        return f"config_{base_name}"
    
    @staticmethod
    def get_firebase_path(data_type: str, identifier: Optional[str] = None) -> str:
        """
        Get standardized Firebase path for data.
        
        Args:
            data_type: Type of data (e.g., 'user_points', 'active_orders')
            identifier: Optional identifier (e.g., user_id, order_number)
            
        Returns:
            Standardized Firebase path
        """
        if identifier:
            return f"{data_type}/{identifier}"
        return data_type
    
    # ========================================================================
    # DATA VALIDATION
    # ========================================================================
    
    @staticmethod
    def validate_temporary_data_usage(data_name: str, operation: str) -> bool:
        """
        Validate that temporary data is being used correctly.
        
        Args:
            data_name: Name of the data structure
            operation: Operation being performed
            
        Returns:
            True if usage is valid
        """
        if data_name.startswith('temp_'):
            # Temporary data should not be persisted to files
            if operation in ['save_to_file', 'write_to_disk', 'backup']:
                logger.error(f"Invalid operation '{operation}' on temporary data '{data_name}'")
                return False
            return True
        return True
    
    @staticmethod
    def validate_config_data_usage(data_name: str, operation: str) -> bool:
        """
        Validate that configuration data is being used correctly.
        
        Args:
            data_name: Name of the data structure
            operation: Operation being performed
            
        Returns:
            True if usage is valid
        """
        if data_name.startswith('config_'):
            # Configuration data should be read-only after initialization
            if operation in ['delete', 'clear', 'modify_structure']:
                logger.warning(f"Potentially unsafe operation '{operation}' on config data '{data_name}'")
                return False
            return True
        return True
    
    # ========================================================================
    # DATA LIFECYCLE MANAGEMENT
    # ========================================================================
    
    @staticmethod
    def get_data_lifecycle_category(data_name: str) -> DataCategory:
        """
        Determine the lifecycle category of data based on its name.
        
        Args:
            data_name: Name of the data structure
            
        Returns:
            DataCategory enum value
        """
        if data_name.startswith('temp_'):
            return DataCategory.TEMPORARY_BUSINESS
        elif data_name.startswith('config_'):
            return DataCategory.STATIC_CONFIG
        elif data_name in ['user_points', 'user_names', 'user_phone_numbers', 
                          'user_emails', 'user_order_history', 'favorite_orders',
                          'delivery_personnel', 'delivery_personnel_earnings']:
            return DataCategory.PERSISTENT_USER
        else:
            return DataCategory.FIREBASE_OPERATION
    
    @staticmethod
    def should_auto_cleanup(data_name: str) -> bool:
        """
        Determine if data should be automatically cleaned up.
        
        Args:
            data_name: Name of the data structure
            
        Returns:
            True if data should be auto-cleaned
        """
        category = DataManagementPatterns.get_data_lifecycle_category(data_name)
        return category == DataCategory.TEMPORARY_BUSINESS
    
    @staticmethod
    def should_persist_to_firebase(data_name: str) -> bool:
        """
        Determine if data should be persisted to Firebase.
        
        Args:
            data_name: Name of the data structure
            
        Returns:
            True if data should be persisted to Firebase
        """
        category = DataManagementPatterns.get_data_lifecycle_category(data_name)
        return category in [DataCategory.PERSISTENT_USER, DataCategory.FIREBASE_OPERATION]
    
    @staticmethod
    def should_cache_locally(data_name: str) -> bool:
        """
        Determine if data should be cached locally for performance.
        
        Args:
            data_name: Name of the data structure
            
        Returns:
            True if data should be cached locally
        """
        category = DataManagementPatterns.get_data_lifecycle_category(data_name)
        return category == DataCategory.STATIC_CONFIG
    
    # ========================================================================
    # MIGRATION UTILITIES
    # ========================================================================
    
    @staticmethod
    def migrate_legacy_data_name(legacy_name: str) -> str:
        """
        Migrate legacy data names to new standardized names.
        
        Args:
            legacy_name: Legacy data name
            
        Returns:
            New standardized data name
        """
        # Mapping of legacy names to new standardized names
        migration_map = {
            'orders': 'temp_orders',
            'order_status': 'temp_order_status',
            'pending_admin_reviews': 'temp_pending_admin_reviews',
            'admin_remarks': 'temp_admin_remarks',
            'awaiting_receipt': 'temp_awaiting_receipt',
            'delivery_locations': 'temp_delivery_locations',
            'current_order_numbers': 'temp_current_order_numbers',
            'delivery_personnel_assignments': 'temp_delivery_personnel_assignments',
            'delivery_personnel_availability': 'temp_delivery_personnel_availability',
            'delivery_personnel_capacity': 'temp_delivery_personnel_capacity',
            'areas_data': 'config_areas_data',
            'restaurants_data': 'config_restaurants_data',
            'menus_data': 'config_menus_data',
            'delivery_locations_data': 'config_delivery_locations_data',
            'delivery_fees_data': 'config_delivery_fees_data'
        }
        
        return migration_map.get(legacy_name, legacy_name)
    
    # ========================================================================
    # DOCUMENTATION HELPERS
    # ========================================================================
    
    @staticmethod
    def get_data_documentation(data_name: str) -> Dict[str, str]:
        """
        Get documentation for a data structure.
        
        Args:
            data_name: Name of the data structure
            
        Returns:
            Dictionary with documentation information
        """
        category = DataManagementPatterns.get_data_lifecycle_category(data_name)
        
        docs = {
            'name': data_name,
            'category': category.value,
            'auto_cleanup': DataManagementPatterns.should_auto_cleanup(data_name),
            'firebase_persist': DataManagementPatterns.should_persist_to_firebase(data_name),
            'local_cache': DataManagementPatterns.should_cache_locally(data_name)
        }
        
        # Add category-specific documentation
        if category == DataCategory.TEMPORARY_BUSINESS:
            docs['description'] = "Temporary business data - automatically cleaned up after order completion"
            docs['storage'] = "Memory only during active processing"
            docs['lifecycle'] = "Cleared after order completion/cancellation"
        elif category == DataCategory.PERSISTENT_USER:
            docs['description'] = "Persistent user data - stored in Firebase"
            docs['storage'] = "Firebase Firestore with local caching"
            docs['lifecycle'] = "Persists until explicitly deleted"
        elif category == DataCategory.STATIC_CONFIG:
            docs['description'] = "Static configuration data - cached locally for performance"
            docs['storage'] = "Local cache loaded from Firebase at startup"
            docs['lifecycle'] = "Loaded at startup, refreshed periodically"
        else:
            docs['description'] = "Firebase operation data"
            docs['storage'] = "Firebase Firestore"
            docs['lifecycle'] = "Managed by Firebase operations"
        
        return docs


# Global instance for easy access
data_patterns = DataManagementPatterns()


def get_data_management_patterns() -> DataManagementPatterns:
    """Get the global data management patterns instance"""
    return data_patterns


def validate_data_operation(data_name: str, operation: str) -> bool:
    """
    Validate a data operation against established patterns.
    
    Args:
        data_name: Name of the data structure
        operation: Operation being performed
        
    Returns:
        True if operation is valid
    """
    patterns = get_data_management_patterns()
    
    # Validate based on data type
    if data_name.startswith('temp_'):
        return patterns.validate_temporary_data_usage(data_name, operation)
    elif data_name.startswith('config_'):
        return patterns.validate_config_data_usage(data_name, operation)
    
    return True


def log_data_operation(data_name: str, operation: str, details: str = ""):
    """
    Log a data operation with standardized formatting.
    
    Args:
        data_name: Name of the data structure
        operation: Operation being performed
        details: Additional details about the operation
    """
    category = data_patterns.get_data_lifecycle_category(data_name)
    logger.info(f"[{category.value.upper()}] {operation} on {data_name} {details}")


def get_all_data_documentation() -> Dict[str, Dict[str, str]]:
    """
    Get documentation for all known data structures.
    
    Returns:
        Dictionary mapping data names to their documentation
    """
    # Known data structures in the system
    known_data = [
        'temp_orders', 'temp_order_status', 'temp_pending_admin_reviews',
        'temp_admin_remarks', 'temp_awaiting_receipt', 'temp_delivery_locations',
        'temp_current_order_numbers', 'temp_delivery_personnel_assignments',
        'temp_delivery_personnel_availability', 'temp_delivery_personnel_capacity',
        'config_areas_data', 'config_restaurants_data', 'config_menus_data',
        'config_delivery_locations_data', 'config_delivery_fees_data',
        'user_points', 'user_names', 'user_phone_numbers', 'user_emails',
        'user_order_history', 'favorite_orders', 'delivery_personnel',
        'delivery_personnel_earnings'
    ]
    
    documentation = {}
    for data_name in known_data:
        documentation[data_name] = data_patterns.get_data_documentation(data_name)
    
    return documentation
