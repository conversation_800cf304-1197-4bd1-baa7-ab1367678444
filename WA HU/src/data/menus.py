"""
Sample menu data for all restaurants - Demo/Presentation version.
This file contains ONLY sample menu items using formal naming conventions.
"""

import logging

logger = logging.getLogger()

# Default sample menu items for restaurants that don't have specific menus
# Using formal naming conventions for demo/presentation purposes
default_menu_items = [
    {"id": 1, "name": "Sample Menu Item 1", "price": 150, "description": "Sample dish 1"},
    {"id": 2, "name": "Sample Menu Item 2", "price": 180, "description": "Sample dish 2"},
    {"id": 3, "name": "Sample Menu Item 3", "price": 200, "description": "Sample dish 3"},
    {"id": 4, "name": "Sample Menu Item 4", "price": 160, "description": "Sample dish 4"},
    {"id": 5, "name": "Sample Menu Item 5", "price": 190, "description": "Sample dish 5"},
    {"id": 6, "name": "Sample Menu Item 6", "price": 170, "description": "Sample dish 6"},
    {"id": 7, "name": "Sample Menu Item 7", "price": 210, "description": "Sample dish 7"},
    {"id": 8, "name": "Sample Menu Item 8", "price": 140, "description": "Sample dish 8"},
    {"id": 9, "name": "Sample Menu Item 9", "price": 220, "description": "Sample dish 9"},
    {"id": 10, "name": "Sample Menu Item 10", "price": 195, "description": "Sample dish 10"},
    {"id": 11, "name": "Sample Menu Item 11", "price": 175, "description": "Sample dish 11"},
]

# Sample menu items for demo/presentation purposes
# Using formal naming conventions: Sample/Test + category + numbers
menus = {
    # Sample Area 1 Restaurants
    1: [  # Test Restaurant 1
        {"id": 1, "name": "Sample Menu Item 1", "price": 150, "description": "Delicious sample dish 1"},
        {"id": 2, "name": "Sample Menu Item 2", "price": 200, "description": "Tasty sample dish 2"},
        {"id": 3, "name": "Sample Menu Item 3", "price": 180, "description": "Popular sample dish 3"},
    ],
    2: [  # Test Restaurant 2
        {"id": 4, "name": "Sample Menu Item 4", "price": 220, "description": "Special sample dish 4"},
        {"id": 5, "name": "Sample Menu Item 5", "price": 160, "description": "Traditional sample dish 5"},
        {"id": 6, "name": "Sample Menu Item 6", "price": 190, "description": "Modern sample dish 6"},
    ],
    # Sample Area 2 Restaurants
    3: [  # Test Restaurant 3
        {"id": 7, "name": "Sample Menu Item 7", "price": 170, "description": "Classic sample dish 7"},
        {"id": 8, "name": "Sample Menu Item 8", "price": 210, "description": "Premium sample dish 8"},
    ],
    4: [  # Test Restaurant 4
        {"id": 9, "name": "Sample Menu Item 9", "price": 140, "description": "Budget sample dish 9"},
        {"id": 10, "name": "Sample Menu Item 10", "price": 250, "description": "Luxury sample dish 10"},
    ],
    # Sample Area 3 Restaurants
    5: [  # Test Restaurant 5
        {"id": 11, "name": "Sample Menu Item 11", "price": 195, "description": "Signature sample dish 11"},
        {"id": 12, "name": "Sample Menu Item 12", "price": 175, "description": "House special sample dish 12"},
    ],
    6: [  # Test Restaurant 6
        {"id": 13, "name": "Sample Menu Item 13", "price": 165, "description": "Chef's choice sample dish 13"},
        {"id": 14, "name": "Sample Menu Item 14", "price": 230, "description": "Gourmet sample dish 14"},
    ],
}

def initialize_menus():
    """Add default sample menus for all restaurants that don't have one defined"""
    # Import here to avoid circular import
    from src.config import restaurants as restaurant_config

    logger.info("Starting sample menu initialization...")
    logger.debug(f"Sample menus type: {type(menus)}")
    logger.debug(f"Sample menus keys: {list(menus.keys())}")

    # Get all restaurant IDs from all areas
    all_restaurant_ids = set()
    for area_restaurants in restaurant_config.values():
        for restaurant_id in area_restaurants.keys():
            # Ensure all restaurant IDs are integers
            try:
                restaurant_id_int = int(restaurant_id)
                all_restaurant_ids.add(restaurant_id_int)
            except (ValueError, TypeError):
                logger.error(f"Invalid restaurant ID in config: {restaurant_id}")
                continue

    logger.info(f"Found {len(all_restaurant_ids)} restaurants in config")

    # For any restaurant without a menu, add a default sample menu
    existing_menu_count = 0
    new_menu_count = 0
    for restaurant_id in all_restaurant_ids:
        if restaurant_id in menus:
            logger.debug(
                f"Sample menu already exists for restaurant ID {restaurant_id} with {len(menus[restaurant_id])} items"
            )
            existing_menu_count += 1
        else:
            menus[restaurant_id] = default_menu_items.copy()
            logger.info(
                f"Created default sample menu for restaurant ID {restaurant_id} with {len(default_menu_items)} items"
            )
            new_menu_count += 1

    # Validate that all menu entries have the required fields
    valid_menus = 0
    invalid_menus = 0
    for restaurant_id, menu_items in menus.items():
        menu_valid = True
        for i, item in enumerate(menu_items):
            if not all(key in item for key in ["id", "name", "price"]):
                logger.warning(
                    f"Sample menu item {i} for restaurant {restaurant_id} is missing required fields"
                )
                menu_valid = False

        if menu_valid:
            valid_menus += 1
        else:
            invalid_menus += 1

    logger.info(f"Sample menu initialization summary:")
    logger.info(f"- Total sample menus: {len(menus)}")
    logger.info(f"- Existing sample menus: {existing_menu_count}")
    logger.info(f"- New default sample menus: {new_menu_count}")
    logger.info(f"- Valid sample menus: {valid_menus}")
    logger.info(f"- Invalid sample menus: {invalid_menus}")
    print("Successfully initialized sample menus!")

# Ensure all menu values are lists to avoid type errors
for key in list(menus.keys()):
    if not isinstance(menus[key], list):
        logger.error(f"Sample menu for restaurant ID {key} is not a list, fixing...")
        menus[key] = default_menu_items.copy()
