"""
Admin handlers for the Wiz Aroma Delivery Bot.
Contains handlers for admin actions.
"""

import datetime
from telebot import types

from src.bot_instance import admin_bot, bot
from src.config import logger, ADMIN_CHAT_IDS
from src.utils.handler_registration import register_handler
from src.data_models import (
    pending_admin_reviews,
    admin_remarks,
    orders,
    order_status,
    current_order_numbers,
)
from src.data_storage import (
    save_order_to_history,
    clean_up_order_data,
    save_admin_remarks as save_admin_remarks_to_storage,
    save_pending_admin_reviews,
    get_points_balance,
)


from src.utils.text_utils import escape_markdown


def escape_markdown_v2(text):
    """Escape special characters for MarkdownV2 format"""
    # Characters that need escaping: _ * [ ] ( ) ~ ` > # + - = | { } !
    # NOTE: Removed '.' from special_chars to fix decimal number display
    special_chars = [
        "_",
        "*",
        "[",
        "]",
        "(",
        ")",
        "~",
        "`",
        ">",
        "#",
        "+",
        "-",
        "=",
        "|",
        "{",
        "}",
        "!",
    ]
    result = str(text)
    for char in special_chars:
        result = result.replace(char, f"\\{char}")
    return result


def send_order_for_review(user_id, order):
    """Send an order for admin review"""
    try:
        # Validate admin chat IDs before proceeding
        if not ADMIN_CHAT_IDS:
            logger.error("No admin chat IDs configured - cannot send order for review")
            raise Exception("No admin chat IDs configured")

        # Validate each admin chat ID
        valid_admin_ids = []
        for admin_id in ADMIN_CHAT_IDS:
            try:
                # Ensure admin_id is an integer
                validated_id = int(admin_id)
                valid_admin_ids.append(validated_id)
            except (ValueError, TypeError) as e:
                logger.error(f"Invalid admin chat ID: {admin_id} - {e}")
                continue

        if not valid_admin_ids:
            logger.error("No valid admin chat IDs found")
            raise Exception("No valid admin chat IDs found")

        # Use the stored order number
        order_number = order.get("order_number") or current_order_numbers.get(user_id)
        if not order_number:
            # Only generate if not already exists
            from src.utils.time_utils import generate_order_number

            order_number = generate_order_number(user_id)
            current_order_numbers[user_id] = order_number
            order["order_number"] = order_number

        # Create consolidated items summary using helper function
        from src.utils.helpers import consolidate_order_items
        items_summary = consolidate_order_items(order["items"])

        # Calculate subtotal and validate delivery fee
        subtotal = sum(item["price"] for item in order["items"])
        delivery_fee = order.get("delivery_fee", 0)

        # Validate delivery fee and log for debugging
        logger.info(f"Admin review for order #{order_number}: subtotal={subtotal}, delivery_fee={delivery_fee}")
        if delivery_fee <= 0:
            logger.warning(f"Invalid delivery fee ({delivery_fee}) in admin review for order #{order_number}")
            # Try to recalculate delivery fee from stored location data
            restaurant_area_id = order.get("restaurant_area_id")
            delivery_location_id = order.get("delivery_location_id")
            if restaurant_area_id and delivery_location_id:
                try:
                    from src.data_storage import get_delivery_fee
                    recalculated_fee = get_delivery_fee(restaurant_area_id, delivery_location_id)
                    if recalculated_fee > 0:
                        delivery_fee = recalculated_fee
                        order["delivery_fee"] = delivery_fee  # Update the order data
                        logger.info(f"Recalculated delivery fee for admin review: {delivery_fee} for area_id={restaurant_area_id}, location_id={delivery_location_id}")
                    else:
                        logger.error(f"Failed to recalculate delivery fee for admin review: area_id={restaurant_area_id}, location_id={delivery_location_id}")
                except Exception as e:
                    logger.error(f"Error recalculating delivery fee for admin review: {e}")

        order["subtotal"] = subtotal
        order["delivery_fee"] = delivery_fee

        # Get user's current points balance
        points_balance = get_points_balance(user_id)
        order["points_balance"] = points_balance

        # Save order to history
        save_order_to_history(user_id, order)

        # Create admin message with points info
        points_info = (
            f"\n💫 Customer Points: {points_balance}" if points_balance > 0 else ""
        )

        # Get order description if available
        order_description = order.get("order_description", "No special instructions")
        # Escape Markdown special characters in user-provided content
        safe_order_description = escape_markdown(order_description)
        description_text = (
            f"\n📝 *Special Instructions:*\n{safe_order_description}"
            if order_description != "No special instructions"
            else ""
        )

        # Get Telegram username if available
        telegram_username = order.get("username", "Not provided")
        if not telegram_username or telegram_username == "No username":
            telegram_username = order.get("telegram_username", "Not provided")
        # Escape Markdown special characters in username
        safe_telegram_username = escape_markdown(telegram_username)

        # Escape Markdown in other user-provided fields
        safe_delivery_name = escape_markdown(order["delivery_name"])
        safe_phone_number = escape_markdown(order["phone_number"])
        safe_restaurant = escape_markdown(order["restaurant"])
        safe_delivery_gate = escape_markdown(order["delivery_gate"])

        admin_message = (
            f"🆕 *NEW ORDER #{order_number}*\n"
            f"━━━━━━━━━━━━━━━━━━━━━\n\n"
            f"👤 *Customer:* {safe_delivery_name}\n"
            f"📱 *Phone:* {safe_phone_number}\n"
            f"🔤 *Telegram:* @{safe_telegram_username}\n"
            f"🏪 *Restaurant:* {safe_restaurant}\n"
            f"📍 *Delivery to:* {safe_delivery_gate}{points_info}\n\n"
            f"📋 *ORDER ITEMS:*\n{items_summary}\n{description_text}\n\n"
            f"💰 *Subtotal:* {subtotal} birr\n"
            f"🚚 *Delivery:* {delivery_fee} birr\n"
            f"💵 *Total:* {subtotal + delivery_fee} birr\n\n"
            f"⏰ *Order Time:* {datetime.datetime.now().strftime('%H:%M:%S')}\n\n"
            "Please review the order and take action:"
        )

        # Create new inline keyboard with renamed buttons and different callback data
        markup = types.InlineKeyboardMarkup()
        markup.row(
            types.InlineKeyboardButton(
                "✓ Accept Order", callback_data=f"new_confirm_{order_number}"
            ),
            types.InlineKeyboardButton(
                "✗ Decline Order", callback_data=f"new_decline_{order_number}"
            ),
        )
        markup.row(
            types.InlineKeyboardButton(
                "📝 Add Note", callback_data=f"new_note_{order_number}"
            )
        )

        # Store order details for admin review FIRST (before sending messages)
        order_review_data = {
            "user_id": user_id,
            "order": order,
            "username": telegram_username,
            "full_name": order["delivery_name"],
        }

        # Store in local memory
        pending_admin_reviews[order_number] = order_review_data

        # Save to Firebase immediately to ensure persistence
        from src.firebase_db import add_pending_admin_review
        firebase_success = add_pending_admin_review(order_number, order_review_data)
        if not firebase_success:
            logger.error(f"Failed to save order #{order_number} to Firebase")
            # Continue anyway but log the issue

        # Also save to local storage as backup
        save_pending_admin_reviews(pending_admin_reviews)
        logger.info(f"Order #{order_number} stored in pending_admin_reviews and saved to Firebase")

        # Send to all valid admins with error handling for each
        successful_sends = 0
        for admin_id in valid_admin_ids:
            try:
                admin_bot.send_message(
                    admin_id, admin_message, reply_markup=markup, parse_mode="Markdown"
                )
                successful_sends += 1
                logger.info(f"Successfully sent order #{order_number} to admin {admin_id}")
            except Exception as send_error:
                logger.error(f"Failed to send order #{order_number} to admin {admin_id}: {send_error}")
                continue

        if successful_sends == 0:
            logger.error(f"Failed to send order #{order_number} to any admin")
            raise Exception("Failed to send order to any admin")

        logger.info(f"Order #{order_number} sent to {successful_sends}/{len(valid_admin_ids)} admins")

    except Exception as e:
        logger.error(f"Error in send_order_for_review: {e}")
        raise


def reload_pending_admin_reviews():
    """Reload pending_admin_reviews from Firebase to ensure we have the latest data"""
    global pending_admin_reviews
    from src.data_storage import USE_FIREBASE

    # Only reload if Firebase is enabled, otherwise keep current data
    if USE_FIREBASE:
        try:
            # Test Firebase connectivity first
            from src.firebase_db import test_firebase_connectivity, sync_pending_admin_reviews_from_firebase
            if not test_firebase_connectivity():
                logger.error("Firebase connectivity test failed - keeping current pending_admin_reviews data")
                return

            # Use the new synchronization function
            fresh_data = sync_pending_admin_reviews_from_firebase()

            # Update the global dictionary
            pending_admin_reviews.clear()
            pending_admin_reviews.update(fresh_data)

            logger.info(
                f"Reloaded pending_admin_reviews from Firebase - found {len(pending_admin_reviews)} pending orders"
            )
        except Exception as e:
            logger.error(f"Error reloading pending_admin_reviews from Firebase: {e}")
            logger.info(f"Keeping current pending_admin_reviews data - {len(pending_admin_reviews)} orders")
    else:
        logger.info(
            f"Firebase disabled - using current pending_admin_reviews data - found {len(pending_admin_reviews)} pending orders"
        )

    return pending_admin_reviews


# New admin action handler with different callback prefixes
@register_handler("admin", handler_type="callback_query", func=lambda call: call.data.startswith(
    ("new_confirm_", "new_decline_", "new_note_")
))
def handle_new_admin_action(call):
    """Handle new admin actions on orders"""
    try:
        # Answer callback immediately to clear loading state
        try:
            admin_bot.answer_callback_query(
                callback_query_id=call.id,
                text="Processing your request...",
                show_alert=False,
            )
        except Exception as callback_error:
            # If callback query is too old, just log it and continue
            if "query is too old" in str(callback_error):
                logger.warning(f"Callback query too old, continuing with processing: {callback_error}")
            else:
                logger.error(f"Error answering callback query: {callback_error}")
            # Continue processing even if callback answer fails

        # Parse callback data
        action_type, order_number = call.data.split("_", 2)[1:]
        logger.info(
            f"Admin {call.from_user.id} processing {action_type} for order {order_number}"
        )

        # Reload pending reviews to ensure we have the latest data
        reload_pending_admin_reviews()

        # Check if order exists
        if order_number not in pending_admin_reviews:
            # Try to get the order directly from Firebase as a fallback
            try:
                from src.firebase_db import get_pending_admin_reviews, test_firebase_connectivity
                firebase_reviews = get_pending_admin_reviews(order_number)
                if firebase_reviews and order_number in firebase_reviews:
                    # Found in Firebase, update local data
                    pending_admin_reviews[order_number] = firebase_reviews[order_number]
                    logger.info(f"Retrieved order {order_number} directly from Firebase")
                else:
                    admin_bot.send_message(
                        call.message.chat.id,
                        f"⚠️ Order #{order_number} has already been processed or doesn't exist.\n"
                        f"Current pending orders: {len(pending_admin_reviews)}\n"
                        f"Firebase connectivity: {'✅' if test_firebase_connectivity() else '❌'}",
                    )
                    return
            except Exception as e:
                logger.error(f"Error checking Firebase for order {order_number}: {e}")
                admin_bot.send_message(
                    call.message.chat.id,
                    f"⚠️ Order #{order_number} not found and Firebase check failed.\n"
                    f"Error: {str(e)[:100]}...\n"
                    f"Current pending orders: {len(pending_admin_reviews)}",
                )
                return

        # Get order details
        order_info = pending_admin_reviews[order_number]
        user_id = order_info["user_id"]
        order = order_info["order"]

        # Handle confirmation (approve)
        if action_type == "confirm":
            # Process approval
            # Send message to user
            try:
                # Get order info from pending reviews first
                order_info = pending_admin_reviews[order_number]
                user_id = order_info["user_id"]
                pending_order = order_info["order"]

                # Make sure the order is in the orders dictionary before updating status
                if user_id not in orders:
                    orders[user_id] = pending_order
                    logger.info(
                        f"Transferred order from pending_admin_reviews to orders for user {user_id}"
                    )

                # Now update the order status
                order_status[user_id] = "AWAITING_PAYMENT_METHOD"
                logger.info(
                    f"Updated order status to AWAITING_PAYMENT_METHOD for user {user_id}"
                )

                # Get the order details now that we've ensured it exists
                order = orders.get(user_id)
                if not order:
                    logger.error(
                        f"No order found for user {user_id} after admin approval"
                    )
                    admin_bot.send_message(
                        call.message.chat.id,
                        f"❌ Error: Order #{order_number} not found in active orders.",
                    )
                    return

                # Calculate total amount
                subtotal = order.get("subtotal", 0)
                delivery_fee = order.get("delivery_fee", 0)
                total_amount = subtotal + delivery_fee

                # Check if user has enough points for delivery fee
                user_point_balance = get_points_balance(user_id)
                has_enough_points = user_point_balance >= delivery_fee

                # Get appropriate payment markup
                from src.utils.keyboards import get_payment_method_markup

                markup = get_payment_method_markup(has_enough_points)

                # Get any notes/remarks for approval reason
                reason = admin_remarks.get(order_number, "")
                reason_text = f"\n\n💬 Admin note: {reason}" if reason else ""

                # Send approval message with payment options - with error handling
                try:
                    bot.send_message(
                        user_id,
                        f"✅ Your order #{order_number} has been approved!{reason_text}\n\n"
                        f"💰 Total Amount: {total_amount} birr\n"
                        f"• Items: {subtotal} birr\n"
                        f"• Delivery: {delivery_fee} birr\n\n"
                        "Please select your payment method:",
                        reply_markup=markup,
                    )
                    logger.info(f"Successfully notified user {user_id} about order #{order_number} approval")
                except Exception as user_notify_error:
                    logger.error(f"Failed to notify user {user_id} about order approval: {user_notify_error}")
                    # Continue with cleanup but notify admin of the issue
                    admin_bot.send_message(
                        call.message.chat.id,
                        f"⚠️ Order #{order_number} approved but failed to notify customer (ID: {user_id}). "
                        f"Error: {str(user_notify_error)[:100]}",
                    )

                # Delete from Firebase directly
                firebase_pending_deleted = False
                firebase_remark_deleted = False

                try:
                    from src.firebase_db import (
                        delete_pending_admin_review,
                        delete_admin_remark,
                    )

                    # Delete from Firebase
                    firebase_pending_deleted = delete_pending_admin_review(order_number)

                    if order_number in admin_remarks:
                        firebase_remark_deleted = delete_admin_remark(order_number)
                        del admin_remarks[order_number]
                    else:
                        firebase_remark_deleted = True  # No remarks to delete

                    if firebase_pending_deleted and firebase_remark_deleted:
                        logger.info(
                            f"Successfully deleted order #{order_number} from Firebase"
                        )
                    else:
                        logger.warning(
                            f"Issue deleting order #{order_number} from Firebase - pending:{firebase_pending_deleted}, remarks:{firebase_remark_deleted}"
                        )
                except Exception as e:
                    logger.error(f"Error deleting from Firebase: {e}")
                    # Continue processing despite Firebase error

                # Always clean up data structures even if Firebase fails
                if order_number in pending_admin_reviews:
                    del pending_admin_reviews[order_number]

                # Save changes to disk
                save_pending_admin_reviews(pending_admin_reviews)
                save_admin_remarks_to_storage(admin_remarks)

                # Send confirmation to admin with note info
                note_info = f" with note: '{reason}'" if reason else ""
                admin_bot.send_message(
                    call.message.chat.id,
                    f"✅ Order #{order_number} successfully confirmed{note_info} and customer has been notified.",
                )

                logger.info(
                    f"Order #{order_number} approved by admin {call.from_user.id}"
                )

            except Exception as e:
                logger.error(
                    f"Error notifying user about order approval: {e}", exc_info=True
                )
                admin_bot.send_message(
                    call.message.chat.id,
                    f"⚠️ Error notifying customer: {str(e)}",
                )

        # Handle decline (reject)
        elif action_type == "decline":
            # Get any notes/remarks for rejection reason
            reason = admin_remarks.get(order_number, "No reason provided")

            # First notify admin we're processing
            admin_bot.send_message(
                call.message.chat.id,
                f"✗ Processing cancellation for order #{order_number}...",
            )

            # Create navigation keyboard
            markup = types.ReplyKeyboardMarkup(row_width=2, resize_keyboard=True)
            markup.add(
                types.KeyboardButton("🍽️ Order Food"),
                types.KeyboardButton("🔙 Back to Main Menu"),
            )

            # Prepare user message
            user_message = (
                f"❌ We're sorry, but your order #{order_number} cannot be processed.\n\n"
                f"Reason: {reason}\n\n"
                "You can place a new order if you wish."
            )

            # Send message to user with enhanced error handling
            message_sent = False
            try:
                # First try to send the message to the user
                bot.send_message(user_id, user_message, reply_markup=markup)
                message_sent = True
                logger.info(
                    f"Cancellation message sent to user {user_id} for order #{order_number}"
                )
            except Exception as user_notify_error:
                logger.error(f"Failed to notify user {user_id} about order decline: {user_notify_error}")
                # Continue with cleanup but notify admin of the issue
                admin_bot.send_message(
                    call.message.chat.id,
                    f"⚠️ Order #{order_number} declined but failed to notify customer (ID: {user_id}). "
                    f"Error: {str(user_notify_error)[:100]}",
                )

                # Delete from Firebase directly
                firebase_pending_deleted = False
                firebase_remark_deleted = False

                try:
                    from src.firebase_db import (
                        delete_pending_admin_review,
                        delete_admin_remark,
                    )

                    # Delete from Firebase
                    firebase_pending_deleted = delete_pending_admin_review(order_number)

                    if order_number in admin_remarks:
                        firebase_remark_deleted = delete_admin_remark(order_number)
                    else:
                        firebase_remark_deleted = True  # No remarks to delete

                    if firebase_pending_deleted and firebase_remark_deleted:
                        logger.info(
                            f"Successfully deleted order #{order_number} from Firebase"
                        )
                    else:
                        logger.warning(
                            f"Issue deleting order #{order_number} from Firebase - pending:{firebase_pending_deleted}, remarks:{firebase_remark_deleted}"
                        )
                except Exception as e:
                    logger.error(f"Error deleting from Firebase: {e}")
                    # Continue processing despite Firebase error

                # Now try to clean up data
                try:
                    clean_up_order_data(user_id, order_number)
                except Exception as cleanup_error:
                    logger.error(
                        f"Error cleaning up order data: {cleanup_error}", exc_info=True
                    )
                    # Continue with other operations even if cleanup fails

                # Try to remove from pending reviews and remarks
                try:
                    if order_number in pending_admin_reviews:
                        del pending_admin_reviews[order_number]
                    if order_number in admin_remarks:
                        del admin_remarks[order_number]
                except Exception as del_error:
                    logger.error(
                        f"Error removing order from pending reviews: {del_error}",
                        exc_info=True,
                    )
                    # Continue with other operations even if deletion fails

                # Try to save changes to disk
                try:
                    save_pending_admin_reviews(pending_admin_reviews)
                    save_admin_remarks_to_storage(admin_remarks)
                except Exception as save_error:
                    logger.error(
                        f"Error saving changes to disk: {save_error}", exc_info=True
                    )
                    # Continue with other operations even if saving fails

                # Send confirmation to admin
                admin_bot.send_message(
                    call.message.chat.id,
                    f"✅ Order #{order_number} has been cancelled and customer has been notified.",
                )

                logger.info(
                    f"Order #{order_number} declined by admin {call.from_user.id}"
                )

            except Exception as e:
                error_msg = f"Error processing order cancellation: {e}"
                logger.error(error_msg, exc_info=True)

                # Provide more specific error message to admin
                if message_sent:
                    admin_bot.send_message(
                        call.message.chat.id,
                        f"⚠️ Customer was notified, but there was an error in cleanup: {str(e)}",
                    )
                else:
                    # Check if the error is related to the user blocking the bot
                    if (
                        "blocked" in str(e).lower()
                        or "user is deactivated" in str(e).lower()
                    ):
                        admin_bot.send_message(
                            call.message.chat.id,
                            f"⚠️ Customer could not be notified: User has blocked the bot or deactivated their account.",
                        )
                    else:
                        admin_bot.send_message(
                            call.message.chat.id,
                            f"⚠️ Error notifying customer: {str(e)}",
                        )

        # Handle note (remarks)
        elif action_type == "note":
            # Ask admin for notes
            admin_bot.send_message(
                call.message.chat.id,
                f"📝 Please enter a note for order #{order_number}:",
                reply_markup=types.ForceReply(selective=True),
            )
            logger.info(
                f"Admin {call.from_user.id} adding note for order {order_number}"
            )

    except Exception as e:
        logger.error(f"Error in handle_new_admin_action: {e}", exc_info=True)
        admin_bot.send_message(
            call.message.chat.id,
            f"⚠️ An error occurred: {str(e)}",
        )


# New handler for admin notes
@admin_bot.message_handler(
    func=lambda message: message.reply_to_message
    and "Please enter a note for order #" in message.reply_to_message.text
)
def save_new_admin_note(message):
    """Save admin note for an order"""
    try:
        # Extract order number
        order_number = message.reply_to_message.text.split("#")[1].strip(":")

        # Save note
        admin_remarks[order_number] = message.text

        # Save to storage
        save_admin_remarks_to_storage(admin_remarks)

        logger.info(
            f"Admin {message.from_user.id} added note for order {order_number}: {message.text[:30]}..."
        )

        # Show confirmation buttons again with new names
        markup = types.InlineKeyboardMarkup()
        markup.row(
            types.InlineKeyboardButton(
                "✓ Accept Order", callback_data=f"new_confirm_{order_number}"
            ),
            types.InlineKeyboardButton(
                "✗ Decline Order", callback_data=f"new_decline_{order_number}"
            ),
        )

        admin_bot.send_message(
            message.chat.id,
            f"✅ Note saved for order #{order_number}. Please choose an action:",
            reply_markup=markup,
        )

    except Exception as e:
        logger.error(f"Error saving admin note: {e}", exc_info=True)
        admin_bot.reply_to(
            message,
            "⚠️ Error saving note. Please try again.",
        )


@register_handler("admin", commands=["start"])
def admin_start(message):
    """Handle the /start command for admin bot"""
    try:
        # Check if the user is an admin
        if message.from_user.id not in ADMIN_CHAT_IDS:
            admin_bot.send_message(
                message.chat.id, "You are not authorized to use this bot."
            )
            return

        # Send welcome message to admin
        markup = types.ReplyKeyboardMarkup(row_width=2, resize_keyboard=True)
        markup.row(types.KeyboardButton("Check Pending Orders"))
        markup.row(types.KeyboardButton("Clear Pending Orders"))
        markup.row(types.KeyboardButton("Clear All and Restart"))
        admin_bot.send_message(
            message.chat.id,
            "Welcome to the Admin Bot. What would you like to do?",
            reply_markup=markup,
        )
    except Exception as e:
        logger.error(f"Error in admin_start: {str(e)}")


@register_handler("admin", func=lambda message: message.text == "Check Pending Orders")
def check_pending_orders(message):
    """Handle the Check Pending Orders button"""
    try:
        # Check if the user is an admin
        if message.from_user.id not in ADMIN_CHAT_IDS:
            admin_bot.send_message(
                message.chat.id, "You are not authorized to use this bot."
            )
            return

        # Reload pending reviews to ensure we have the latest data
        from src.data_storage import USE_FIREBASE

        if USE_FIREBASE:
            from src.firebase_db import (
                get_pending_admin_reviews as firebase_get_pending_reviews,
            )

            # Get fresh data from Firebase
            try:
                firebase_pending_orders = firebase_get_pending_reviews()
                if firebase_pending_orders:
                    pending_admin_reviews.clear()  # Clear existing local data
                    pending_admin_reviews.update(
                        firebase_pending_orders
                    )  # Update with fresh data
                    logger.info(
                        f"Successfully refreshed pending orders from Firebase: {len(pending_admin_reviews)} orders found"
                    )
                else:
                    # If no orders in Firebase, use local data
                    logger.info(
                        "Using local pending orders data after empty Firebase response"
                    )
            except Exception as e:
                logger.error(f"Error getting pending orders from Firebase: {e}")
                # Continue with local data if Firebase fails
        else:
            logger.info(
                f"Firebase disabled - using current pending orders data: {len(pending_admin_reviews)} orders found"
            )

        # Check if there are any pending orders
        if not pending_admin_reviews:
            admin_bot.send_message(
                message.chat.id,
                "🔍 There are no pending orders awaiting your decision.",
            )
            return

        # Send total count
        admin_bot.send_message(
            message.chat.id,
            f"📋 Found {len(pending_admin_reviews)} pending order(s):",
        )

        # Display each pending order
        for order_number, order_data in pending_admin_reviews.items():
            try:
                # Validate order data
                if not isinstance(order_data, dict) or "order" not in order_data:
                    admin_bot.send_message(
                        message.chat.id,
                        f"⚠️ Error: Invalid data for order #{order_number}",
                    )
                    continue

                # Get order details
                order = order_data["order"]
                user_id = order_data.get("user_id", "Unknown")

                # Create a clean formatted order summary
                plain_message = f"📋 Order #{order_number}\n\n"

                # Customer info
                customer_name = order.get("delivery_name", "Unknown")
                phone = order.get("phone_number", "No phone")
                username = order.get(
                    "username", order.get("telegram_username", "No username")
                )
                plain_message += f"👤 Customer: {customer_name}\n"
                plain_message += f"📱 Phone: {phone}\n"
                if username:
                    plain_message += f"🔤 Telegram: @{username}\n"

                # Restaurant info
                restaurant_name = order.get("restaurant", "Unknown restaurant")
                restaurant_area = order.get("restaurant_area", "Unknown area")
                plain_message += (
                    f"🏪 Restaurant: {restaurant_name} ({restaurant_area})\n"
                )

                # Delivery info
                delivery_gate = order.get("delivery_gate", "Unknown gate")
                delivery_fee = order.get("delivery_fee", 0)
                plain_message += (
                    f"📍 Delivery to: {delivery_gate} ({delivery_fee} birr)\n\n"
                )

                # Items
                plain_message += "📋 ORDER ITEMS:\n"
                items = order.get("items", [])

                if not items:
                    plain_message += "- No items found\n"
                else:
                    # Use consolidated items format
                    from src.utils.helpers import consolidate_order_items
                    plain_message += consolidate_order_items(items) + "\n"

                # Special instructions
                description = order.get("order_description", "")
                if description and description != "No special instructions":
                    plain_message += f"\n📝 Special Instructions:\n{description}\n"

                # Financial info
                subtotal = order.get(
                    "subtotal", sum(item.get("price", 0) for item in items)
                )
                total = subtotal + delivery_fee
                plain_message += (
                    f"\n💰 Subtotal: {subtotal} birr\n"
                    f"🚚 Delivery: {delivery_fee} birr\n"
                    f"💵 Total: {total} birr\n"
                )

                # Points info if available
                points_balance = order.get("points_balance", None)
                if points_balance is not None:
                    plain_message += f"💫 Customer Points: {points_balance}\n"

                # Order time
                created_at = order.get("created_at", "Unknown time")
                if created_at:
                    plain_message += f"\n⏰ Order Time: {created_at}\n"

                # Create inline keyboard for admin actions
                markup = types.InlineKeyboardMarkup(row_width=2)

                # Add admin action buttons in rows
                markup.row(
                    types.InlineKeyboardButton(
                        "✅ Approve", callback_data=f"new_confirm_{order_number}"
                    ),
                    types.InlineKeyboardButton(
                        "❌ Decline", callback_data=f"new_decline_{order_number}"
                    ),
                )
                markup.row(
                    types.InlineKeyboardButton(
                        "📝 Add Note", callback_data=f"new_note_{order_number}"
                    )
                )

                # Send message with inline keyboard as plain text (no Markdown)
                admin_bot.send_message(
                    message.chat.id, plain_message, reply_markup=markup
                )
                logger.info(
                    f"Successfully displayed pending order #{order_number} to admin"
                )

            except Exception as e:
                # Log the error but continue processing other orders
                logger.error(
                    f"Error displaying order #{order_number}: {e}", exc_info=True
                )
                admin_bot.send_message(
                    message.chat.id,
                    f"⚠️ Error displaying order #{order_number}. Please try the Clear All and Restart option if this persists.",
                )

    except Exception as e:
        logger.error(f"Error in check_pending_orders: {e}", exc_info=True)
        admin_bot.send_message(
            message.chat.id,
            f"⚠️ Error retrieving pending orders: {str(e)}",
        )


def register_handlers():
    """Register all handlers in this module"""
    # All handlers are already registered using decorators
    pass


@admin_bot.message_handler(commands=["clear_pending"])
def clear_pending_orders(message):
    """Clear all pending orders - Admin only command"""
    try:
        # Check if the user is an admin
        if message.from_user.id not in ADMIN_CHAT_IDS:
            admin_bot.send_message(
                message.chat.id, "You are not authorized to use this bot."
            )
            return

        # Ask for confirmation
        markup = types.InlineKeyboardMarkup()
        markup.row(
            types.InlineKeyboardButton(
                "✓ Yes, clear all", callback_data="confirm_clear_pending"
            ),
            types.InlineKeyboardButton(
                "✗ No, cancel", callback_data="cancel_clear_pending"
            ),
        )

        # Get count of pending orders
        count = len(pending_admin_reviews)

        admin_bot.send_message(
            message.chat.id,
            f"⚠️ Are you sure you want to clear all {count} pending orders? This action cannot be undone.",
            reply_markup=markup,
        )

    except Exception as e:
        logger.error(f"Error in clear_pending_orders: {str(e)}", exc_info=True)
        admin_bot.send_message(
            message.chat.id,
            f"⚠️ An error occurred: {str(e)}",
        )


@register_handler("admin", handler_type="callback_query", func=lambda call: call.data in ["confirm_clear_pending", "cancel_clear_pending"])
def handle_clear_pending_confirmation(call):
    """Handle confirmation for clearing pending orders"""
    try:
        # Answer callback immediately to clear loading state
        admin_bot.answer_callback_query(
            callback_query_id=call.id,
            text="Processing your request...",
            show_alert=False,
        )

        if call.data == "cancel_clear_pending":
            admin_bot.edit_message_text(
                "Operation cancelled. No orders were cleared.",
                call.message.chat.id,
                call.message.message_id,
            )
            return

        # Proceed with clearing
        if call.data == "confirm_clear_pending":
            try:
                # First try to clear from Firebase
                from src.firebase_db import (
                    clear_pending_admin_reviews,
                    clear_admin_remarks,
                )

                # Clear from Firebase first
                firebase_pending_cleared = clear_pending_admin_reviews()
                firebase_remarks_cleared = clear_admin_remarks()

                if firebase_pending_cleared and firebase_remarks_cleared:
                    logger.info(
                        "Successfully cleared all pending orders and remarks from Firebase"
                    )
                else:
                    logger.warning("Issue clearing pending orders from Firebase")

                # Then clear from local memory
                pending_count = len(pending_admin_reviews)
                pending_admin_reviews.clear()
                admin_remarks.clear()

                # Save changes to local storage
                save_pending_admin_reviews(pending_admin_reviews)
                save_admin_remarks_to_storage(admin_remarks)

                # Edit the message to confirm clearing
                admin_bot.edit_message_text(
                    f"✅ Successfully cleared {pending_count} pending orders and related remarks.",
                    call.message.chat.id,
                    call.message.message_id,
                )

                logger.info(
                    f"Admin {call.from_user.id} cleared {pending_count} pending orders"
                )

            except Exception as e:
                error_msg = f"Error clearing pending orders: {e}"
                logger.error(error_msg, exc_info=True)

                # Edit the message to show the error
                admin_bot.edit_message_text(
                    f"⚠️ Error clearing pending orders: {str(e)}",
                    call.message.chat.id,
                    call.message.message_id,
                )
    except Exception as e:
        logger.error(f"Error in handle_clear_pending_confirmation: {e}", exc_info=True)
        try:
            admin_bot.send_message(
                call.message.chat.id,
                f"⚠️ An error occurred: {str(e)}",
            )
        except:
            pass


@register_handler("admin", func=lambda message: message.text == "Clear Pending Orders")
def clear_pending_orders_button(message):
    """Handle the Clear Pending Orders button click"""
    # Just call the command handler with the same functionality
    clear_pending_orders(message)


@register_handler("admin", func=lambda message: message.text == "Clear All and Restart")
def clear_all_and_restart_button(message):
    """Handle the Clear All and Restart button click"""
    # Just call the command handler with the same functionality
    clear_all_command(message)


@register_handler("admin", commands=["clear_all"])
def clear_all_command(message):
    """Admin command to clear all pending orders and restart the bot"""
    try:
        # Check if the user is an admin
        if message.from_user.id not in ADMIN_CHAT_IDS:
            admin_bot.send_message(
                message.chat.id, "You are not authorized to use this bot."
            )
            return

        # Directly clear the data
        pending_admin_reviews.clear()
        admin_remarks.clear()

        # Save the empty data
        save_pending_admin_reviews(pending_admin_reviews)
        save_admin_remarks_to_storage(admin_remarks)

        # Try to clear directly from Firebase
        try:
            from src.firebase_db import clear_pending_admin_reviews, clear_admin_remarks

            clear_pending_admin_reviews()
            clear_admin_remarks()
        except Exception as e:
            logger.error(f"Error clearing Firebase data: {e}")

        # Send confirmation
        admin_bot.send_message(
            message.chat.id,
            "✅ All pending orders have been cleared. The bot will restart now.",
        )

        # Force restart the bot by exiting - system service should restart it
        logger.info("Admin requested full restart to clear all pending orders")
        import os
        import sys

        os._exit(0)  # Force immediate exit to trigger restart

    except Exception as e:
        logger.error(f"Error in clear_all_command: {str(e)}", exc_info=True)
        admin_bot.send_message(
            message.chat.id,
            f"⚠️ An error occurred: {str(e)}",
        )
