"""
Location handlers for the maintenance bot.
Contains handlers for managing delivery locations.
"""

from telebot import types

from src.bot_instance import maintenance_bot
from src.config import MAINTENANCE_CHAT_ID
from src.data_storage import (
    # Area functions
    get_all_areas,
    get_area_by_id,
    # Location functions
    get_all_delivery_locations,
    get_delivery_location_by_id,
    add_delivery_location,
    update_delivery_location,
    delete_delivery_location,
    # Delivery fee functions
    get_all_delivery_fees,
    add_delivery_fee,
    delete_delivery_fee,
)

# Dictionary to store user states
user_states = {}


def is_authorized(user_id):
    """Check if user is authorized to use the maintenance bot"""
    return str(user_id) == MAINTENANCE_CHAT_ID


def show_delivery_menu(message):
    """Show the delivery locations management menu"""
    user_id = message.from_user.id

    if not is_authorized(user_id):
        return

    markup = types.ReplyKeyboardMarkup(row_width=2, resize_keyboard=True)
    markup.add(
        types.KeyboardButton("List Delivery Locations"),
        types.KeyboardButton("Add Delivery Location"),
        types.KeyboardButton("Update Delivery Location"),
        types.KeyboardButton("Delete Delivery Location"),
        types.KeyboardButton("Back to Main Menu"),
    )

    maintenance_bot.send_message(
        user_id,
        "Delivery Locations Management. What would you like to do?",
        reply_markup=markup,
    )


@maintenance_bot.message_handler(
    func=lambda message: message.text == "List Delivery Locations"
)
def list_delivery_locations(message):
    """List all delivery locations grouped by areas with prices"""
    user_id = message.from_user.id

    if not is_authorized(user_id):
        return

    # Get all areas
    areas = get_all_areas()
    if not areas:
        # Create a keyboard with navigation options
        markup = types.ReplyKeyboardMarkup(row_width=2, resize_keyboard=True)
        markup.add(
            types.KeyboardButton("Back to Delivery Menu"),
            types.KeyboardButton("Back to Main Menu"),
        )

        maintenance_bot.send_message(
            user_id, "No areas found. Please add an area first.", reply_markup=markup
        )
        return

    # Get all delivery locations
    locations = get_all_delivery_locations()
    if not locations:
        # Create a keyboard with navigation options
        markup = types.ReplyKeyboardMarkup(row_width=2, resize_keyboard=True)
        markup.add(
            types.KeyboardButton("Add Delivery Location"),
            types.KeyboardButton("Back to Delivery Menu"),
            types.KeyboardButton("Back to Main Menu"),
        )

        maintenance_bot.send_message(
            user_id, "No delivery locations found.", reply_markup=markup
        )
        return

    # Get all delivery fees
    fees = get_all_delivery_fees()

    # Organize locations and fees by area
    locations_by_area = {}
    for area in areas:
        locations_by_area[area["id"]] = {"area_name": area["name"], "locations": []}

    # Find fees for each location in each area
    for location in locations:
        for area in areas:
            # Find fee for this area-location pair
            fee_amount = 0
            for fee in fees:
                if (
                    fee["area_id"] == area["id"]
                    and fee["location_id"] == location["id"]
                ):
                    fee_amount = fee["fee"]
                    break

            # Add location with fee to this area
            locations_by_area[area["id"]]["locations"].append(
                {"id": location["id"], "name": location["name"], "fee": fee_amount}
            )

    # Generate locations text grouped by area
    locations_text = "Delivery Locations by Area:\n\n"
    for area_id, area_data in locations_by_area.items():
        locations_text += f"Area: {area_data['area_name']}\n"
        for loc in area_data["locations"]:
            fee_text = f"{loc['fee']} ETB" if loc["fee"] > 0 else "Not set"
            locations_text += f"  ID: {loc['id']} - {loc['name']} - Fee: {fee_text}\n"
        locations_text += "\n"

    # Create a keyboard with navigation options
    markup = types.ReplyKeyboardMarkup(row_width=2, resize_keyboard=True)
    markup.add(
        types.KeyboardButton("Add Delivery Location"),
        types.KeyboardButton("Update Delivery Location"),
        types.KeyboardButton("Delete Delivery Location"),
        types.KeyboardButton("Back to Delivery Menu"),
        types.KeyboardButton("Back to Main Menu"),
    )

    maintenance_bot.send_message(user_id, locations_text, reply_markup=markup)


@maintenance_bot.message_handler(
    func=lambda message: message.text == "Update Delivery Location"
)
def update_delivery_location_handler(message):
    """Update a delivery location - first select location"""
    user_id = message.from_user.id

    if not is_authorized(user_id):
        return

    # Get all delivery locations
    locations = get_all_delivery_locations()
    if not locations:
        # Create a keyboard with navigation options
        markup = types.ReplyKeyboardMarkup(row_width=2, resize_keyboard=True)
        markup.add(
            types.KeyboardButton("Add Delivery Location"),
            types.KeyboardButton("Back to Delivery Menu"),
            types.KeyboardButton("Back to Main Menu"),
        )

        maintenance_bot.send_message(
            user_id, "No delivery locations found to update.", reply_markup=markup
        )
        return

    # Show locations to select from
    locations_text = "Delivery Locations:\n\n"
    for location in locations:
        locations_text += f"ID: {location['id']} - {location['name']}\n"

    locations_text += "\nEnter the ID of the location you want to update:"

    # Add a cancel button
    markup = types.InlineKeyboardMarkup()
    markup.add(types.InlineKeyboardButton("Cancel", callback_data="cancel_update"))

    msg = maintenance_bot.send_message(user_id, locations_text, reply_markup=markup)

    user_states[user_id] = {"state": "selecting_location_to_update"}
    maintenance_bot.register_next_step_handler(
        msg, process_location_selection_for_update
    )


def process_location_selection_for_update(message):
    """Process location selection for update"""
    user_id = message.from_user.id

    if not is_authorized(user_id):
        return

    try:
        location_id = int(message.text.strip())
    except ValueError:
        maintenance_bot.send_message(
            user_id, "Please enter a valid location ID (number)."
        )
        update_delivery_location_handler(message)
        return

    # Get the location
    location = get_delivery_location_by_id(location_id)
    if not location:
        maintenance_bot.send_message(
            user_id, f"Location with ID {location_id} not found."
        )
        update_delivery_location_handler(message)
        return

    # Show update options
    markup = types.ReplyKeyboardMarkup(row_width=2, resize_keyboard=True)
    markup.add(
        types.KeyboardButton(f"Update Name for Location {location_id}"),
        types.KeyboardButton(f"Update Price for Location {location_id}"),
        types.KeyboardButton("Back to Delivery Menu"),
        types.KeyboardButton("Back to Main Menu"),
    )

    maintenance_bot.send_message(
        user_id,
        f"Location: {location['name']} (ID: {location_id})\n\nWhat would you like to update?",
        reply_markup=markup,
    )

    # Store location ID in state
    user_states[user_id] = {
        "state": "updating_location",
        "location_id": location_id,
    }


@maintenance_bot.message_handler(
    func=lambda message: message.text
    and message.text.startswith("Update Name for Location ")
)
def ask_update_location_name(message):
    """Ask for new name for location"""
    user_id = message.from_user.id

    if not is_authorized(user_id):
        return

    # Extract location ID from message
    try:
        location_id = int(message.text.replace("Update Name for Location ", "").strip())
    except ValueError:
        maintenance_bot.send_message(user_id, "Invalid location ID.")
        update_delivery_location_handler(message)
        return

    # Get the location
    location = get_delivery_location_by_id(location_id)
    if not location:
        maintenance_bot.send_message(
            user_id, f"Location with ID {location_id} not found."
        )
        update_delivery_location_handler(message)
        return

    # Ask for new name
    markup = types.ForceReply(selective=True)
    msg = maintenance_bot.send_message(
        user_id,
        f"Current name: {location['name']}\nEnter the new name for the delivery location:",
        reply_markup=markup,
    )

    user_states[user_id] = {
        "state": "updating_location_name",
        "location_id": location_id,
    }
    maintenance_bot.register_next_step_handler(msg, process_update_location_name)


def process_update_location_name(message):
    """Process updating location name"""
    user_id = message.from_user.id

    if not is_authorized(user_id):
        return

    new_name = message.text.strip()
    if not new_name:
        maintenance_bot.send_message(user_id, "Location name cannot be empty.")
        return

    location_id = user_states[user_id].get("location_id")
    if not location_id:
        maintenance_bot.send_message(user_id, "Location ID not found.")
        update_delivery_location_handler(message)
        return

    # Update the location name
    updated_location = update_delivery_location(location_id, new_name)

    if updated_location:
        # Create a keyboard with navigation options
        markup = types.ReplyKeyboardMarkup(row_width=2, resize_keyboard=True)
        markup.add(
            types.KeyboardButton("Update Another Delivery Location"),
            types.KeyboardButton("List Delivery Locations"),
            types.KeyboardButton("Back to Delivery Menu"),
            types.KeyboardButton("Back to Main Menu"),
        )

        maintenance_bot.send_message(
            user_id,
            f"Location name updated successfully to: {updated_location['name']}",
            reply_markup=markup,
        )
    else:
        maintenance_bot.send_message(user_id, "Failed to update location name.")
        update_delivery_location_handler(message)


@maintenance_bot.message_handler(
    func=lambda message: message.text
    and message.text.startswith("Update Price for Location ")
)
def ask_select_area_for_price_update(message):
    """Ask to select area for price update"""
    user_id = message.from_user.id

    if not is_authorized(user_id):
        return

    # Extract location ID from message
    try:
        location_id = int(
            message.text.replace("Update Price for Location ", "").strip()
        )
    except ValueError:
        maintenance_bot.send_message(user_id, "Invalid location ID.")
        update_delivery_location_handler(message)
        return

    # Get the location
    location = get_delivery_location_by_id(location_id)
    if not location:
        maintenance_bot.send_message(
            user_id, f"Location with ID {location_id} not found."
        )
        update_delivery_location_handler(message)
        return

    # Get all areas
    areas = get_all_areas()
    if not areas:
        # Create a keyboard with navigation options
        markup = types.ReplyKeyboardMarkup(row_width=2, resize_keyboard=True)
        markup.add(
            types.KeyboardButton("Back to Delivery Menu"),
            types.KeyboardButton("Back to Main Menu"),
        )

        maintenance_bot.send_message(
            user_id, "No areas found. Please add an area first.", reply_markup=markup
        )
        return

    # Get all delivery fees for this location
    fees = get_all_delivery_fees()
    location_fees = {}
    for fee in fees:
        if fee["location_id"] == location_id:
            location_fees[fee["area_id"]] = fee["fee"]

    # Show areas with current fees
    areas_text = f"Select an area to update price for {location['name']}:\n\n"
    for area in areas:
        fee = location_fees.get(area["id"], 0)
        fee_text = f"{fee} ETB" if fee > 0 else "Not set"
        areas_text += f"ID: {area['id']} - {area['name']} - Current Fee: {fee_text}\n"

    areas_text += "\nEnter the ID of the area you want to update the price for:"

    # Add a cancel button
    markup = types.InlineKeyboardMarkup()
    markup.add(types.InlineKeyboardButton("Cancel", callback_data="cancel_update"))

    msg = maintenance_bot.send_message(user_id, areas_text, reply_markup=markup)

    user_states[user_id] = {
        "state": "selecting_area_for_price_update",
        "location_id": location_id,
    }
    maintenance_bot.register_next_step_handler(
        msg, process_area_selection_for_price_update
    )


def process_area_selection_for_price_update(message):
    """Process area selection for price update"""
    user_id = message.from_user.id

    if not is_authorized(user_id):
        return

    try:
        area_id = int(message.text.strip())
    except ValueError:
        maintenance_bot.send_message(user_id, "Please enter a valid area ID (number).")
        update_delivery_location_handler(message)
        return

    # Get the area
    area = get_area_by_id(area_id)
    if not area:
        maintenance_bot.send_message(user_id, f"Area with ID {area_id} not found.")
        update_delivery_location_handler(message)
        return

    location_id = user_states[user_id].get("location_id")
    if not location_id:
        maintenance_bot.send_message(user_id, "Location ID not found.")
        update_delivery_location_handler(message)
        return

    # Get the location
    location = get_delivery_location_by_id(location_id)
    if not location:
        maintenance_bot.send_message(
            user_id, f"Location with ID {location_id} not found."
        )
        update_delivery_location_handler(message)
        return

    # Get current fee
    fees = get_all_delivery_fees()
    current_fee = 0
    for fee in fees:
        if fee["area_id"] == area_id and fee["location_id"] == location_id:
            current_fee = fee["fee"]
            break

    # Ask for new fee
    markup = types.ForceReply(selective=True)
    msg = maintenance_bot.send_message(
        user_id,
        f"Current fee from {area['name']} to {location['name']}: {current_fee} ETB\nEnter the new fee amount:",
        reply_markup=markup,
    )

    user_states[user_id] = {
        "state": "updating_location_fee",
        "location_id": location_id,
        "area_id": area_id,
    }
    maintenance_bot.register_next_step_handler(msg, process_update_location_fee)


def process_update_location_fee(message):
    """Process updating location fee"""
    user_id = message.from_user.id

    if not is_authorized(user_id):
        return

    try:
        new_fee = float(message.text.strip())
    except ValueError:
        maintenance_bot.send_message(
            user_id, "Please enter a valid fee amount (number)."
        )
        return

    location_id = user_states[user_id].get("location_id")
    area_id = user_states[user_id].get("area_id")

    if not location_id or not area_id:
        maintenance_bot.send_message(user_id, "Location ID or area ID not found.")
        update_delivery_location_handler(message)
        return

    # Get location and area names for better UX
    location = get_delivery_location_by_id(location_id)
    area = get_area_by_id(area_id)

    if not location or not area:
        maintenance_bot.send_message(user_id, "Location or area not found.")
        update_delivery_location_handler(message)
        return

    # Update the fee
    updated_fee = add_delivery_fee(area_id, location_id, new_fee)

    if updated_fee:
        # Create a keyboard with navigation options
        markup = types.ReplyKeyboardMarkup(row_width=2, resize_keyboard=True)
        markup.add(
            types.KeyboardButton("Update Another Delivery Location"),
            types.KeyboardButton("List Delivery Locations"),
            types.KeyboardButton("Back to Delivery Menu"),
            types.KeyboardButton("Back to Main Menu"),
        )

        maintenance_bot.send_message(
            user_id,
            f"Delivery fee updated successfully!\nFrom: {area['name']}\nTo: {location['name']}\nNew Fee: {updated_fee['fee']} ETB",
            reply_markup=markup,
        )
    else:
        maintenance_bot.send_message(user_id, "Failed to update delivery fee.")
        update_delivery_location_handler(message)


@maintenance_bot.message_handler(
    func=lambda message: message.text == "Update Another Delivery Location"
)
def update_another_delivery_location(message):
    """Handle update another delivery location button"""
    user_id = message.from_user.id

    if not is_authorized(user_id):
        return

    # Clear any state
    if user_id in user_states:
        user_states.pop(user_id)

    # Go to update delivery location flow
    update_delivery_location_handler(message)


@maintenance_bot.message_handler(
    func=lambda message: message.text == "Delete Delivery Location"
)
def delete_delivery_location_handler(message):
    """Delete a delivery location - first select location"""
    user_id = message.from_user.id

    if not is_authorized(user_id):
        return

    # Get all delivery locations
    locations = get_all_delivery_locations()
    if not locations:
        # Create a keyboard with navigation options
        markup = types.ReplyKeyboardMarkup(row_width=2, resize_keyboard=True)
        markup.add(
            types.KeyboardButton("Add Delivery Location"),
            types.KeyboardButton("Back to Delivery Menu"),
            types.KeyboardButton("Back to Main Menu"),
        )

        maintenance_bot.send_message(
            user_id, "No delivery locations found to delete.", reply_markup=markup
        )
        return

    # Show locations to select from
    locations_text = "Delivery Locations:\n\n"
    for location in locations:
        locations_text += f"ID: {location['id']} - {location['name']}\n"

    locations_text += "\nEnter the ID of the location you want to delete:"

    # Add a cancel button
    markup = types.InlineKeyboardMarkup()
    markup.add(types.InlineKeyboardButton("Cancel", callback_data="cancel_update"))

    msg = maintenance_bot.send_message(user_id, locations_text, reply_markup=markup)

    user_states[user_id] = {"state": "selecting_location_to_delete"}
    maintenance_bot.register_next_step_handler(
        msg, process_location_selection_for_delete
    )


def process_location_selection_for_delete(message):
    """Process location selection for delete"""
    user_id = message.from_user.id

    if not is_authorized(user_id):
        return

    try:
        location_id = int(message.text.strip())
    except ValueError:
        maintenance_bot.send_message(
            user_id, "Please enter a valid location ID (number)."
        )
        delete_delivery_location_handler(message)
        return

    # Get the location
    location = get_delivery_location_by_id(location_id)
    if not location:
        maintenance_bot.send_message(
            user_id, f"Location with ID {location_id} not found."
        )
        delete_delivery_location_handler(message)
        return

    # Check if there are any fees associated with this location
    fees = get_all_delivery_fees()
    location_fees = [fee for fee in fees if fee["location_id"] == location_id]

    # Show delete confirmation options
    markup = types.ReplyKeyboardMarkup(row_width=2, resize_keyboard=True)
    markup.add(
        types.KeyboardButton(f"Confirm Delete Location {location_id}"),
        types.KeyboardButton("Cancel Delete"),
        types.KeyboardButton("Back to Delivery Menu"),
        types.KeyboardButton("Back to Main Menu"),
    )

    confirmation_text = f"Are you sure you want to delete this delivery location?\n\nID: {location['id']}\nName: {location['name']}"

    if location_fees:
        confirmation_text += (
            f"\n\nThis will also delete {len(location_fees)} associated delivery fees."
        )

    confirmation_text += "\n\nThis action cannot be undone."

    maintenance_bot.send_message(user_id, confirmation_text, reply_markup=markup)

    user_states[user_id] = {
        "state": "confirming_location_delete",
        "location_id": location_id,
    }


@maintenance_bot.message_handler(
    func=lambda message: message.text
    and message.text.startswith("Confirm Delete Location ")
    and user_states.get(message.from_user.id, {}).get("state")
    == "confirming_location_delete"
)
def confirm_delete_location(message):
    """Confirm and process location deletion"""
    user_id = message.from_user.id

    if not is_authorized(user_id):
        return

    location_id = user_states[user_id].get("location_id")
    if not location_id:
        maintenance_bot.send_message(user_id, "Location ID not found.")
        delete_delivery_location_handler(message)
        return

    # Get the location
    location = get_delivery_location_by_id(location_id)
    if not location:
        maintenance_bot.send_message(
            user_id, f"Location with ID {location_id} not found."
        )
        delete_delivery_location_handler(message)
        return

    # Get all fees associated with this location
    fees = get_all_delivery_fees()
    location_fees = [fee for fee in fees if fee["location_id"] == location_id]
    fee_count = len(location_fees)

    # Delete all associated fees first
    for fee in location_fees:
        delete_delivery_fee(fee["area_id"], fee["location_id"])

    # Delete the location
    success = delete_delivery_location(location_id)

    if success:
        # Create a keyboard with navigation options
        markup = types.ReplyKeyboardMarkup(row_width=2, resize_keyboard=True)
        markup.add(
            types.KeyboardButton("Delete Another Delivery Location"),
            types.KeyboardButton("List Delivery Locations"),
            types.KeyboardButton("Back to Delivery Menu"),
            types.KeyboardButton("Back to Main Menu"),
        )

        result_text = f"Delivery location '{location['name']}' deleted successfully!"
        if fee_count > 0:
            result_text += f"\n{fee_count} associated delivery fees were also deleted."

        maintenance_bot.send_message(user_id, result_text, reply_markup=markup)
    else:
        maintenance_bot.send_message(user_id, "Failed to delete delivery location.")
        delete_delivery_location_handler(message)


@maintenance_bot.message_handler(
    func=lambda message: message.text == "Cancel Delete"
    and user_states.get(message.from_user.id, {}).get("state")
    == "confirming_location_delete"
)
def cancel_delete_location(message):
    """Cancel location deletion"""
    user_id = message.from_user.id

    if not is_authorized(user_id):
        return

    # Clear the state
    if user_id in user_states:
        user_states.pop(user_id)

    maintenance_bot.send_message(user_id, "Delivery location deletion cancelled.")
    list_delivery_locations(message)


@maintenance_bot.message_handler(
    func=lambda message: message.text == "Delete Another Delivery Location"
)
def delete_another_delivery_location(message):
    """Handle delete another delivery location button"""
    user_id = message.from_user.id

    if not is_authorized(user_id):
        return

    # Clear any state
    if user_id in user_states:
        user_states.pop(user_id)

    # Go to delete delivery location flow
    delete_delivery_location_handler(message)


@maintenance_bot.message_handler(
    func=lambda message: message.text == "Back to Delivery Menu"
)
def back_to_delivery_menu(message):
    """Go back to the delivery menu"""
    user_id = message.from_user.id

    if not is_authorized(user_id):
        return

    # Clear any state
    if user_id in user_states:
        user_states.pop(user_id)

    # Go back to delivery menu
    show_delivery_menu(message)


@maintenance_bot.message_handler(
    func=lambda message: message.text == "Back to Main Menu"
)
def back_to_main_menu(message):
    """Go back to the main menu"""
    user_id = message.from_user.id

    if not is_authorized(user_id):
        return

    # Clear any state
    if user_id in user_states:
        user_states.pop(user_id)

    # Create a keyboard with main menu options
    markup = types.ReplyKeyboardMarkup(row_width=2, resize_keyboard=True)
    markup.add(
        types.KeyboardButton("Menu"),
        types.KeyboardButton("Delivery Locations & Fees"),
        types.KeyboardButton("Areas"),
        types.KeyboardButton("Settings"),
    )

    maintenance_bot.send_message(
        user_id,
        "Welcome to the Maintenance Bot. What would you like to manage?",
        reply_markup=markup,
    )


@maintenance_bot.callback_query_handler(func=lambda call: call.data == "cancel_update")
def cancel_update_callback(call):
    """Handle cancel button click"""
    user_id = call.from_user.id

    if not is_authorized(user_id):
        return

    # Clear any state
    if user_id in user_states:
        user_states.pop(user_id)

    # Edit the message to show it was cancelled
    maintenance_bot.edit_message_text(
        "Operation cancelled.",
        chat_id=call.message.chat.id,
        message_id=call.message.message_id,
    )

    # Show the delivery menu
    show_delivery_menu(call.message)
