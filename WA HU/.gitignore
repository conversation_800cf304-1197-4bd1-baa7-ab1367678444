# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class

# C extensions
*.so

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
#  Usually these files are written by a python script from a template
#  before PyInstaller builds the exe, so as to inject date/other infos into it.
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/

# Translations
*.mo
*.pot

# Django stuff:
*.log
bot.log
local_settings.py
db.sqlite3
db.sqlite3-journal

# Flask stuff:
instance/
.webassets-cache

# Scrapy stuff:
.scrapy

# Sphinx documentation
docs/_build/

# PyBuilder
target/

# Jupyter Notebook
.ipynb_checkpoints

# pyenv
.python-version

# Virtual environments
.venv/
venv/
env/
ENV/

# Environment variables
.env
.env.local
.env.production
.env.development

# Firebase credentials
firebase-credentials.json
*firebase-adminsdk*.json
firebase-service-account*.json
service-account*.json

# API Keys and Secrets
*.key
*.pem
secrets.json
config.json

# User data files
user_names.json
user_phone_numbers.json
user_order_history.json

# Backup files
*.backup

# IDE files
.idea/
.vscode/
*.swp
*.swo

# OS specific files
.DS_Store
Thumbs.db
# Documentation
delivery_fees.txt
menu_and_restaurants.txt

# Temporary files and development artifacts
test_*.py
demo_*.py
fix_*.py
comprehensive_cleanup.py
*_temp.py
*.tmp

# Development and testing
.pytest_cache/
.coverage
htmlcov/
.tox/