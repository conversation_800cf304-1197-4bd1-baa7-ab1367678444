# ⚠️ Proprietary Notice

This project is proprietary and confidential. No part of this project may be copied, distributed, or used without explicit written permission from the author. See LICENSE for details.

<div align="center">

# 🍽️ Wiz Aroma Food Delivery System v2.1

### *Enterprise-Grade Intelligent Food Delivery Management Platform*

[![Python](https://img.shields.io/badge/Python-3.9+-blue.svg)](https://python.org)
[![Telegram Bot API](https://img.shields.io/badge/Telegram-Bot%20API-blue.svg)](https://core.telegram.org/bots/api)
[![Firebase](https://img.shields.io/badge/Firebase-Firestore%20%26%20Realtime%20DB-orange.svg)](https://firebase.google.com)
[![License](https://img.shields.io/badge/License-Proprietary-red.svg)](LICENSE)
[![Version](https://img.shields.io/badge/Version-2.1-brightgreen.svg)](https://github.com/Mih-Nig-Afe/Wiz-Aroma-Food-Delivery/releases/tag/v2.1)
[![Architecture](https://img.shields.io/badge/Architecture-Clean%20%26%20Professional-success.svg)](#-architecture-highlights)

*A comprehensive multi-bot Telegram-based food delivery system with Firebase-first architecture, automatic data lifecycle management, and professional enterprise-grade automation capabilities*

[🚀 Features](#-current-features) • [📋 Documentation](#-documentation) • [🔧 Installation](#-installation--setup) • [🎯 Architecture](#-architecture-highlights) • [📈 Roadmap](#-release-notes--roadmap)

</div>

---

## 🎯 **Project Overview**

Wiz Aroma Food Delivery v2.1 is an enterprise-grade multi-bot Telegram delivery system currently serving **50-120 orders daily** with **Firebase-first architecture**, **automatic data lifecycle management**, and **production-ready automation**. The system features a sophisticated clean architecture with specialized bots for different operational functions, broadcast-based order assignment, and comprehensive management capabilities with professional data handling.

## 🚀 **Current Features**

### 👥 **Customer Experience**

- 🏪 **Multi-Restaurant Selection**: Browse restaurants by geographical area with Firebase-stored data
- 📱 **Smart Menu System**: Intuitive categorized interface with real-time pricing from Firebase
- 💫 **Points Reward System**: Earn 10 + 1% of delivery fee as loyalty points (minimum 1 point)
- 💳 **Multiple Payment Methods**: Telebirr, CBE Bank, BOA Bank, Points redemption
- ⭐ **Favorite Orders**: One-click reordering of preferred meals stored in Firebase
- 📍 **Complete Order Lifecycle**: Delivery personnel completion + customer confirmation system
- ⏰ **Operating Hours**: Flexible working hours for weekdays and weekends

### 🤖 **Multi-Bot Architecture**

- **🛍️ User Bot**: Customer ordering interface with consolidated messaging
- **📊 Management Bot**: Comprehensive analytics, personnel management (50% delivery fee sharing), and data operations
- **🚚 Delivery Bot**: Broadcast-based order assignment with 5-order limit per delivery personnel
- **� Order Tracking Bot**: Internal order tracking with customer contact functionality

### 🗄️ **Advanced Data Management System v2.1**

- **🔥 Firebase-First Architecture**: All persistent business data stored exclusively in Firebase Firestore
- **🧹 Automatic Data Lifecycle Management**: Temporary business data automatically cleaned after order completion
- **⚡ Professional Data Patterns**: Standardized naming conventions (`temp_*`, `config_*`, `firebase_*`)
- **🔄 Intelligent Data Sync**: Real-time synchronization with automatic cleanup mechanisms
- **📊 Real-time Analytics**: Live data synchronization and comprehensive reporting
- **🛡️ Enterprise Security**: Role-based access control with clean data separation
- **📋 Audit Logging**: Comprehensive operation tracking and security
- **💾 Backup & Recovery**: Automated data archiving with 24-hour recovery window
- **🔐 Access Control**: Multi-layer authorization for sensitive operations

### 🛠️ **Technical Features**

- **🔥 Firebase-Exclusive Integration**: All data stored and accessed exclusively from Firebase Firestore (no local fallbacks)
- **� Enterprise Security**: Dynamic authorization via Firebase, role-based access control, zero hardcoded credentials
- **� Comprehensive Logging**: Audit trails, error tracking, and performance monitoring
- **⚡ High Performance**: 99.5% uptime with advanced monitoring and optimization
- **� Broadcast Order Assignment**: Orders sent to all available delivery personnel with first-come-first-served logic
- **� Point-Based Payment System**: Integrated loyalty points with 50% delivery fee sharing for personnel
- **� Consolidated Messaging**: Single message updates with editing instead of multiple notifications
- **🔄 Real-time Data Sync**: Live Firebase synchronization across all system components

## 📱 **Quick Start Guide**

### 🛍️ **For Customers**

1. **Start Ordering**: Send `/start` to **User Bot**
2. **Main Menu Options**:
   - 🍽️ **Order Food** - Browse and order from restaurants
   - 💫 **My Points** - Check loyalty points balance
   - ⭐ **My Favorites** - Quick reorder saved meals
   - ℹ️ **Help** - Get assistance and support

3. **Ordering Process**:

   ```
   Select Area → Choose Restaurant → Add Items →
   Delivery Details → Payment → Verification → Delivery
   ```

### 👨‍💼 **For Administrators**

- **Order Management**: Review and approve/reject incoming orders
- **Customer Communication**: Direct messaging with customers
- **System Monitoring**: Track order flow and system performance

## 🔧 **Installation & Setup**

### Prerequisites

```bash
Python 3.9+
Firebase Account
Telegram Bot Tokens
```

### Quick Installation

```bash
# Clone the repository
git clone https://github.com/Mih-Nig-Afe/Wiz-Aroma-Food-Delivery.git
cd Wiz-Aroma-Food-Delivery

# Install dependencies
pip install -r requirements.txt

# Configure environment variables
cp .env.example .env
# Edit .env with your bot tokens and Firebase credentials

# Run the system
python main.py
```

## 📂 **Project Structure**

The project follows a clean, organized structure:

```text
Wiz-Aroma-V-1.3.3/
├── 📋 Core Files
│   ├── README.md                    # This file
│   ├── main.py                     # Application entry point
│   ├── requirements.txt            # Dependencies
│   └── LICENSE                     # Project license
│
├── 📚 docs/                        # All documentation
├── 🎨 assets/                      # Logos and assets
├── 📜 scripts/                     # Utility scripts
├── 🧪 tests/                       # Test files
├── 📊 logs/                        # Application logs
└── 📦 src/                         # Source code
    ├── bots/                       # Bot implementations
    ├── handlers/                   # Message handlers
    └── utils/                      # Utility modules
```

## 📋 **Documentation**

### 🛠️ **Technical Documentation**

- **[SYSTEM_ARCHITECTURE.md](docs/SYSTEM_ARCHITECTURE.md)** - System architecture overview
- **[PROJECT_STRUCTURE.md](docs/PROJECT_STRUCTURE.md)** - Project structure details
- **[FIREBASE_SETUP.md](docs/FIREBASE_SETUP.md)** - Firebase integration guide
- **[DEPLOYMENT.md](docs/DEPLOYMENT.md)** - Deployment instructions
- **[SECURITY.md](docs/SECURITY.md)** - Security best practices

## 🔒 **Security & Production Features**

### **🛡️ Enterprise-Grade Security**

- **🔐 Zero Hardcoded Credentials:** All sensitive data externalized to environment variables
- **🚫 GitHub-Safe Repository:** No production credentials exposed in source code
- **🔑 Dynamic Authorization:** Real-time access control via Firebase Firestore
- **📊 Comprehensive Audit Logging:** All admin actions tracked with timestamps
- **⚡ Rate Limiting:** Admin actions limited to prevent abuse (10/minute)
- **🚨 Error Reporting:** Critical errors automatically reported to administrators
- **✅ Graceful Error Handling:** Robust fallback mechanisms for all operations
- **🔄 Environment Validation:** System exits safely if required credentials missing

### **🎯 Access Control**

- **Admin Management:** Only authorized Telegram IDs can access administrative features
- **Role-Based Permissions:** Different access levels for different bot functions
- **Secure Fallbacks:** No hardcoded credentials in any fallback mechanisms
- **Production Ready:** Safe for public GitHub hosting and open-source distribution

## 🏗️ **Architecture Highlights**

### **🔥 Firebase-First Data Architecture v2.1**

The system now implements a **clean, professional architecture** with clear separation of concerns:

#### **📊 Data Classification System**

```
📁 TEMPORARY BUSINESS DATA (temp_*)
├── Automatic cleanup after order completion
├── Session-based storage only
└── No permanent local persistence

📁 PERSISTENT USER DATA (Firebase-Only)
├── User profiles and preferences
├── Order history and analytics
└── Delivery personnel data

📁 STATIC CONFIGURATION (config_*)
├── Areas, restaurants, menus
├── Locally cached for performance
└── Loaded at system startup
```

#### **🧹 Automatic Data Lifecycle Management**

- **Order Completion Cleanup**: All temporary data automatically removed
- **Session Timeout Handling**: 30-minute user session cleanup
- **Stale Data Detection**: Orphaned data automatically identified and removed
- **Force Cleanup Service**: Hourly comprehensive cleanup to prevent accumulation

#### **⚡ Professional Code Organization**

- **Standardized Naming**: `temp_*`, `config_*`, `firebase_*` conventions
- **Clean Architecture**: Proper separation between business logic and data storage
- **Enterprise Patterns**: Professional data management throughout the codebase
- **Backward Compatibility**: Smooth transition with legacy support

---

## 📊 **Release Notes & Roadmap**

- For the latest release details, see [RELEASE_NOTES_V2.1.md](docs/RELEASE_NOTES_V2.1.md).

### **Version Roadmap**

| Version | Status      | Key Features                                                    | Release Date |
|---------|-------------|----------------------------------------------------------------|-------------|
| V1.3.3  | Released    | Multi-bot system, Firebase integration                         | July 2025   |
| V2.0    | Released    | Firebase-exclusive storage, broadcast order assignment, point-based payments, 50% delivery fee sharing, management analytics | August 2025 |
| V2.1    | **Current** | **Firebase-first architecture, automatic data lifecycle management, professional code organization, clean architecture patterns** | **September 2025** |
| V3.0    | Planned     | AI automation, intelligent distribution                        | Q4 2025     |
| V4.0    | Future      | Mobile app, multi-language support                             | Q1 2026     |

---

<div align="center">

### 🌟 **Star this repository if you find it helpful!**

**Made by [Mihretab Nigatu](https://github.com/Mih-Nig-Afe)**

*Transforming food delivery through intelligent automation*

</div>
