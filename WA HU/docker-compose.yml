services:
  wiz-aroma-bot:
    build: .
    container_name: wiz-aroma-delivery-bot
    restart: unless-stopped
    env_file:
      - .env
    volumes:
      # Mount data directory for persistence
      - ./data_files:/app/data_files
      # Mount Firebase credentials file if using file-based auth
      - ./firebase-credentials.json:/app/firebase-credentials.json:ro
    
    # Health check
    healthcheck:
      test: ["CMD", "python", "-c", "import psutil; exit(0 if any('python' in p.name() for p in psutil.process_iter()) else 1)"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    
    # Logging configuration
    logging:
      driver: json-file
      options:
        max-size: "10m"
        max-file: "3"

  # Optional: Add a separate service for each bot type
  wiz-aroma-user-bot:
    build: .
    container_name: wiz-aroma-user-bot
    restart: unless-stopped
    env_file:
      - .env
    volumes:
      - ./data_files:/app/data_files
      - ./firebase-credentials.json:/app/firebase-credentials.json:ro
    command: ["python", "main.py", "--bot", "user"]
    profiles:
      - separate-bots

  wiz-aroma-admin-bot:
    build: .
    container_name: wiz-aroma-admin-bot
    restart: unless-stopped
    env_file:
      - .env
    volumes:
      - ./data_files:/app/data_files
      - ./firebase-credentials.json:/app/firebase-credentials.json:ro
    command: ["python", "main.py", "--bot", "admin"]
    profiles:
      - separate-bots

volumes:
  data_files:
    driver: local 