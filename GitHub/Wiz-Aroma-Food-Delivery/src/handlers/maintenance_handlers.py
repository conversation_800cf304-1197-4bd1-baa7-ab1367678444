"""
Maintenance handlers for the Wiz Aroma Delivery Bot.
Contains handlers for maintenance actions to manage areas, restaurants, menus, etc.
"""

import json
import os
import logging
from telebot import types

from src.bot_instance import maintenance_bot
from src.utils.handler_registration import register_handler
from src.config import (
    MAINTENANCE_CHAT_ID,
    POINTS_FILE,
    ORDER_HISTORY_FILE,
    USER_NAMES_FILE,
    USER_PHONE_NUMBERS_FILE,
    USER_EMAILS_FILE,
    AREAS_FILE,
    RESTAURANTS_FILE,
    MENUS_FILE,
    DELIVERY_LOCATIONS_FILE,
    DELIVERY_FEES_FILE,
    FAVORITE_ORDERS_FILE,
    CURRENT_ORDERS_FILE,
    ORDER_STATUS_FILE,
    PENDING_ADMIN_REVIEWS_FILE,
    ADMIN_REMARKS_FILE,
    AWAITING_RECEIPT_FILE,
    DELIVERY_LOCATIONS_TEMP_FILE,
    USER_ORDER_COUNTS_FILE,
    CURRENT_ORDER_NUMBERS_FILE,
)
from src.data_storage import (
    # Area functions
    get_all_areas,
    get_area_by_id,
    add_area,
    update_area,
    delete_area,
    # Restaurant functions
    get_all_restaurants,
    get_restaurants_by_area,
    get_restaurant_by_id,
    add_restaurant,
    update_restaurant,
    delete_restaurant,
    # Menu functions
    get_restaurant_menu,
    add_menu_item,
    update_menu_item,
    delete_menu_item,
    # Delivery location functions
    get_all_delivery_locations,
    get_delivery_location_by_id,
    add_delivery_location,
    update_delivery_location,
    delete_delivery_location,
    # Delivery fee functions
    get_all_delivery_fees,
    add_delivery_fee,
    delete_delivery_fee,
    # Data loading functions
    load_points,
    load_order_history,
    load_user_names,
    load_user_phone_numbers,
    load_user_emails,
    load_favorite_orders,
    load_current_orders,
    load_order_status,
    load_pending_admin_reviews,
    load_admin_remarks,
    load_awaiting_receipt,
    load_delivery_locations_temp,
    load_user_order_counts,
    load_current_order_numbers,
)

# Initialize logger
logger = logging.getLogger(__name__)

# State storage for maintenance bot
user_states = {}


@register_handler("maintenance", commands=["export_data"])
def export_data(message):
    """Export all data files for backup or synchronization"""
    user_id = message.from_user.id

    if not is_authorized(user_id):
        maintenance_bot.send_message(
            user_id, "You are not authorized to use this command."
        )
        return

    # Collect all data
    data = {
        "user_points": load_points(),
        "user_order_history": load_order_history(),
        "user_names": load_user_names(),
        "user_phone_numbers": load_user_phone_numbers(),
        "user_emails": load_user_emails(),
        "favorite_orders": load_favorite_orders(),
        "current_orders": load_current_orders(),
        "order_status": load_order_status(),
        "pending_admin_reviews": load_pending_admin_reviews(),
        "admin_remarks": load_admin_remarks(),
        "awaiting_receipt": load_awaiting_receipt(),
        "delivery_locations_temp": load_delivery_locations_temp(),
        "user_order_counts": load_user_order_counts(),
        "current_order_numbers": load_current_order_numbers(),
    }

    # Add file paths for reference
    file_paths = {
        "user_points": POINTS_FILE,
        "user_order_history": ORDER_HISTORY_FILE,
        "user_names": USER_NAMES_FILE,
        "user_phone_numbers": USER_PHONE_NUMBERS_FILE,
        "user_emails": USER_EMAILS_FILE,
        "favorite_orders": FAVORITE_ORDERS_FILE,
        "current_orders": CURRENT_ORDERS_FILE,
        "order_status": ORDER_STATUS_FILE,
        "pending_admin_reviews": PENDING_ADMIN_REVIEWS_FILE,
        "admin_remarks": ADMIN_REMARKS_FILE,
        "awaiting_receipt": AWAITING_RECEIPT_FILE,
        "delivery_locations_temp": DELIVERY_LOCATIONS_TEMP_FILE,
        "user_order_counts": USER_ORDER_COUNTS_FILE,
        "current_order_numbers": CURRENT_ORDER_NUMBERS_FILE,
        "areas": AREAS_FILE,
        "restaurants": RESTAURANTS_FILE,
        "menus": MENUS_FILE,
        "delivery_locations": DELIVERY_LOCATIONS_FILE,
        "delivery_fees": DELIVERY_FEES_FILE,
    }

    # Add configuration data
    data["areas"] = get_all_areas()
    data["restaurants"] = get_all_restaurants()
    data["delivery_locations"] = get_all_delivery_locations()
    data["delivery_fees"] = get_all_delivery_fees()

    # Add file paths to the data
    data["_file_paths"] = file_paths

    # Convert to JSON
    try:
        json_data = json.dumps(data, indent=2)

        # Send as a file if it's not too large
        if len(json_data) < 50000:  # Telegram has a message size limit
            maintenance_bot.send_document(
                user_id,
                json_data.encode("utf-8"),
                caption="All system data",
                visible_file_name="wiz_aroma_data.json",
            )
        else:
            # Split into multiple files if too large
            maintenance_bot.send_message(
                user_id,
                "Data is too large to send as a single file. Sending in parts...",
            )

            # Send user data
            user_data = {
                "user_points": data["user_points"],
                "user_order_history": data["user_order_history"],
                "user_names": data["user_names"],
                "user_phone_numbers": data["user_phone_numbers"],
                "user_emails": data["user_emails"],
            }
            user_json = json.dumps(user_data, indent=2)
            maintenance_bot.send_document(
                user_id,
                user_json.encode("utf-8"),
                caption="User data",
                visible_file_name="user_data.json",
            )

            # Send order data
            order_data = {
                "favorite_orders": data["favorite_orders"],
                "current_orders": data["current_orders"],
                "order_status": data["order_status"],
                "pending_admin_reviews": data["pending_admin_reviews"],
                "admin_remarks": data["admin_remarks"],
                "awaiting_receipt": data["awaiting_receipt"],
                "user_order_counts": data["user_order_counts"],
                "current_order_numbers": data["current_order_numbers"],
            }
            order_json = json.dumps(order_data, indent=2)
            maintenance_bot.send_document(
                user_id,
                order_json.encode("utf-8"),
                caption="Order data",
                visible_file_name="order_data.json",
            )

            # Send configuration data
            config_data = {
                "areas": data["areas"],
                "restaurants": data["restaurants"],
                "delivery_locations": data["delivery_locations"],
                "delivery_fees": data["delivery_fees"],
                "_file_paths": data["_file_paths"],
            }
            config_json = json.dumps(config_data, indent=2)
            maintenance_bot.send_document(
                user_id,
                config_json.encode("utf-8"),
                caption="Configuration data",
                visible_file_name="config_data.json",
            )

        maintenance_bot.send_message(
            user_id,
            "Data export complete. You can use this data to update your GitHub repository.",
        )
    except Exception as e:
        maintenance_bot.send_message(user_id, f"Error exporting data: {str(e)}")


# Check if user is authorized to use maintenance bot
def is_authorized(user_id):
    """Check if a user is authorized to use the maintenance bot"""
    return str(user_id) == str(MAINTENANCE_CHAT_ID)


# Helper function to group restaurants by area
def group_restaurants_by_area(restaurants):
    """Group restaurants by area for better display"""
    restaurants_by_area = {}
    for restaurant in restaurants:
        area_id = restaurant["area_id"]
        if area_id not in restaurants_by_area:
            area = get_area_by_id(area_id)
            area_name = area["name"] if area else f"Unknown Area ({area_id})"
            restaurants_by_area[area_id] = {"name": area_name, "restaurants": []}

        restaurants_by_area[area_id]["restaurants"].append(restaurant)

    return restaurants_by_area


@register_handler("maintenance", commands=["start"])
def maintenance_start(message):
    """Handle the /start command for maintenance bot"""
    user_id = message.from_user.id

    if not is_authorized(user_id):
        maintenance_bot.send_message(user_id, "You are not authorized to use this bot.")
        return

    markup = types.ReplyKeyboardMarkup(row_width=2, resize_keyboard=True)
    markup.add(
        types.KeyboardButton("Areas"),
        types.KeyboardButton("Restaurants"),
        types.KeyboardButton("Menus"),
        types.KeyboardButton("Delivery Locations & Fees"),
    )

    maintenance_bot.send_message(
        user_id,
        "Welcome to the Maintenance Bot. What would you like to manage?",
        reply_markup=markup,
    )


# Area Management
@maintenance_bot.message_handler(func=lambda message: message.text == "Areas")
def show_areas_menu(message):
    """Show the areas management menu"""
    user_id = message.from_user.id

    if not is_authorized(user_id):
        return

    markup = types.ReplyKeyboardMarkup(row_width=2, resize_keyboard=True)
    markup.add(
        types.KeyboardButton("List Areas"),
        types.KeyboardButton("Add Area"),
        types.KeyboardButton("Update Area"),
        types.KeyboardButton("Delete Area"),
        types.KeyboardButton("Back to Main Menu"),
    )

    maintenance_bot.send_message(
        user_id, "Areas Management. What would you like to do?", reply_markup=markup
    )


@maintenance_bot.message_handler(func=lambda message: message.text == "List Areas")
def list_areas(message):
    """List all areas"""
    user_id = message.from_user.id

    if not is_authorized(user_id):
        return

    areas = get_all_areas()

    if not areas:
        maintenance_bot.send_message(user_id, "No areas found.")
        return

    areas_text = "Areas:\n\n"
    for area in areas:
        areas_text += f"ID: {area['id']} - {area['name']}\n"

    maintenance_bot.send_message(user_id, areas_text)


@maintenance_bot.message_handler(func=lambda message: message.text == "Add Area")
def ask_area_name(message):
    """Ask for area name to add"""
    user_id = message.from_user.id

    if not is_authorized(user_id):
        return

    markup = types.ForceReply(selective=True)
    msg = maintenance_bot.send_message(
        user_id, "Enter the name of the new area:", reply_markup=markup
    )

    user_states[user_id] = {"state": "adding_area"}
    maintenance_bot.register_next_step_handler(msg, process_add_area)


def process_add_area(message):
    """Process adding a new area"""
    user_id = message.from_user.id

    if not is_authorized(user_id):
        return

    area_name = message.text.strip()

    if not area_name:
        maintenance_bot.send_message(user_id, "Area name cannot be empty.")
        return

    new_area = add_area(area_name)

    if new_area:
        # Create a keyboard with navigation options
        markup = types.ReplyKeyboardMarkup(row_width=2, resize_keyboard=True)
        markup.add(
            types.KeyboardButton("Add Another Area"),
            types.KeyboardButton("Back to Areas Menu"),
            types.KeyboardButton("Back to Main Menu"),
        )

        maintenance_bot.send_message(
            user_id,
            f"Area added successfully! ID: {new_area['id']} - {new_area['name']}",
            reply_markup=markup,
        )
    else:
        maintenance_bot.send_message(user_id, "Failed to add area.")


@maintenance_bot.message_handler(
    func=lambda message: message.text == "Add Another Area"
)
def add_another_area(message):
    """Handle add another area button"""
    user_id = message.from_user.id

    if not is_authorized(user_id):
        return

    # Clear any state
    if user_id in user_states:
        user_states.pop(user_id)

    # Go to add area flow
    ask_area_name(message)


@maintenance_bot.message_handler(
    func=lambda message: message.text == "Update Area"
    and not user_states.get(message.from_user.id, {}).get("state")
    == "updating_restaurant"
)
def ask_area_id_to_update(message):
    """Ask for area ID to update"""
    user_id = message.from_user.id

    if not is_authorized(user_id):
        return

    areas = get_all_areas()

    if not areas:
        maintenance_bot.send_message(user_id, "No areas found to update.")
        return

    areas_text = "Areas:\n\n"
    for area in areas:
        areas_text += f"ID: {area['id']} - {area['name']}\n"

    areas_text += "\nEnter the ID of the area you want to update:"

    markup = types.ForceReply(selective=True)
    msg = maintenance_bot.send_message(user_id, areas_text, reply_markup=markup)

    user_states[user_id] = {"state": "updating_area_id"}
    maintenance_bot.register_next_step_handler(msg, process_area_id_to_update)


def process_area_id_to_update(message):
    """Process area ID to update"""
    user_id = message.from_user.id

    if not is_authorized(user_id):
        return

    try:
        area_id = int(message.text.strip())
    except ValueError:
        maintenance_bot.send_message(user_id, "Please enter a valid ID (number).")
        return

    area = get_area_by_id(area_id)

    if not area:
        maintenance_bot.send_message(user_id, f"Area with ID {area_id} not found.")
        return

    user_states[user_id] = {"state": "updating_area_name", "area_id": area_id}

    markup = types.ForceReply(selective=True)
    msg = maintenance_bot.send_message(
        user_id,
        f"Current name: {area['name']}\nEnter the new name for the area:",
        reply_markup=markup,
    )

    maintenance_bot.register_next_step_handler(msg, process_update_area)


def process_update_area(message):
    """Process updating area name"""
    user_id = message.from_user.id

    if not is_authorized(user_id):
        return

    area_name = message.text.strip()

    if not area_name:
        maintenance_bot.send_message(user_id, "Area name cannot be empty.")
        return

    area_id = user_states[user_id].get("area_id")
    if not area_id:
        maintenance_bot.send_message(user_id, "Area ID not found.")
        return

    updated_area = update_area(area_id, area_name)

    if updated_area:
        # Create a keyboard with navigation options
        markup = types.ReplyKeyboardMarkup(row_width=2, resize_keyboard=True)
        markup.add(
            types.KeyboardButton("Update Another Area"),
            types.KeyboardButton("Back to Areas Menu"),
            types.KeyboardButton("Back to Main Menu"),
        )

        maintenance_bot.send_message(
            user_id,
            f"Area updated successfully! ID: {updated_area['id']} - {updated_area['name']}",
            reply_markup=markup,
        )
    else:
        maintenance_bot.send_message(user_id, "Failed to update area.")


@maintenance_bot.message_handler(func=lambda message: message.text == "Delete Area")
def ask_area_id_to_delete(message):
    """Ask for area ID to delete"""
    user_id = message.from_user.id

    if not is_authorized(user_id):
        return

    areas = get_all_areas()

    if not areas:
        maintenance_bot.send_message(user_id, "No areas found to delete.")
        return

    areas_text = "Areas:\n\n"
    for area in areas:
        areas_text += f"ID: {area['id']} - {area['name']}\n"

    areas_text += "\nEnter the ID of the area you want to delete:"

    markup = types.ForceReply(selective=True)
    msg = maintenance_bot.send_message(user_id, areas_text, reply_markup=markup)

    user_states[user_id] = {"state": "deleting_area"}
    maintenance_bot.register_next_step_handler(msg, process_delete_area)


def process_delete_area(message):
    """Process deleting an area"""
    user_id = message.from_user.id

    if not is_authorized(user_id):
        return

    try:
        area_id = int(message.text.strip())
    except ValueError:
        maintenance_bot.send_message(user_id, "Please enter a valid ID (number).")
        return

    area = get_area_by_id(area_id)

    if not area:
        maintenance_bot.send_message(user_id, f"Area with ID {area_id} not found.")
        return

    # Check if any restaurants are using this area
    restaurants = get_restaurants_by_area(area_id)
    if restaurants:
        restaurant_names = ", ".join([r["name"] for r in restaurants])

        # Create a keyboard with navigation options
        markup = types.ReplyKeyboardMarkup(row_width=2, resize_keyboard=True)
        markup.add(
            types.KeyboardButton("Delete Another Area"),
            types.KeyboardButton("Back to Areas Menu"),
            types.KeyboardButton("Back to Main Menu"),
        )

        maintenance_bot.send_message(
            user_id,
            f"Cannot delete area with ID {area_id} because it is used by the following restaurants: {restaurant_names}",
            reply_markup=markup,
        )
        return

    # Confirm deletion
    markup = types.ReplyKeyboardMarkup(row_width=2, resize_keyboard=True)
    markup.add(
        types.KeyboardButton(f"Confirm Delete Area {area_id}"),
        types.KeyboardButton("Cancel Area Delete"),
        types.KeyboardButton("Back to Areas Menu"),
        types.KeyboardButton("Back to Main Menu"),
    )

    maintenance_bot.send_message(
        user_id,
        f"Are you sure you want to delete this area?\n\nID: {area_id}\nName: {area['name']}\n\nThis action cannot be undone.",
        reply_markup=markup,
    )

    user_states[user_id] = {"state": "confirming_area_delete", "area_id": area_id}


@maintenance_bot.message_handler(
    func=lambda message: message.text
    and message.text.startswith("Confirm Delete Area ")
    and user_states.get(message.from_user.id, {}).get("state")
    == "confirming_area_delete"
)
def confirm_delete_area(message):
    """Confirm and process area deletion"""
    user_id = message.from_user.id

    if not is_authorized(user_id):
        return

    area_id = user_states[user_id].get("area_id")
    if not area_id:
        maintenance_bot.send_message(user_id, "Area ID not found.")
        show_areas_menu(message)
        return

    # Get area info before deletion for confirmation message
    area = get_area_by_id(area_id)
    if not area:
        maintenance_bot.send_message(user_id, f"Area with ID {area_id} not found.")
        show_areas_menu(message)
        return

    area_name = area["name"]

    # Delete the area
    success = delete_area(area_id)

    if success:
        # Create a keyboard with navigation options
        markup = types.ReplyKeyboardMarkup(row_width=2, resize_keyboard=True)
        markup.add(
            types.KeyboardButton("Delete Another Area"),
            types.KeyboardButton("Back to Areas Menu"),
            types.KeyboardButton("Back to Main Menu"),
        )

        maintenance_bot.send_message(
            user_id,
            f"Area deleted successfully!\nID: {area_id} - {area_name}",
            reply_markup=markup,
        )
    else:
        maintenance_bot.send_message(user_id, "Failed to delete area.")
        show_areas_menu(message)


@maintenance_bot.message_handler(
    func=lambda message: message.text == "Cancel Area Delete"
    and user_states.get(message.from_user.id, {}).get("state")
    == "confirming_area_delete"
)
def cancel_delete_area(message):
    """Cancel area deletion"""
    user_id = message.from_user.id

    if not is_authorized(user_id):
        return

    # Clear the state
    if user_id in user_states:
        user_states.pop(user_id)

    maintenance_bot.send_message(user_id, "Area deletion cancelled.")
    show_areas_menu(message)


@maintenance_bot.message_handler(
    func=lambda message: message.text == "Delete Another Area"
)
def delete_another_area(message):
    """Handle delete another area button"""
    user_id = message.from_user.id

    if not is_authorized(user_id):
        return

    # Clear any state
    if user_id in user_states:
        user_states.pop(user_id)

    # Go to delete area flow
    ask_area_id_to_delete(message)


@maintenance_bot.message_handler(
    func=lambda message: message.text == "Back to Main Menu"
)
def back_to_main_menu(message):
    """Return to the main menu"""
    user_id = message.from_user.id

    if not is_authorized(user_id):
        return

    markup = types.ReplyKeyboardMarkup(row_width=2, resize_keyboard=True)
    markup.add(
        types.KeyboardButton("Areas"),
        types.KeyboardButton("Restaurants"),
        types.KeyboardButton("Menus"),
        types.KeyboardButton("Delivery Locations & Fees"),
    )

    maintenance_bot.send_message(
        user_id, "Main Menu. What would you like to manage?", reply_markup=markup
    )


# Restaurant Management
@maintenance_bot.message_handler(func=lambda message: message.text == "Restaurants")
def show_restaurants_menu(message):
    """Show the restaurants management menu"""
    user_id = message.from_user.id

    if not is_authorized(user_id):
        return

    markup = types.ReplyKeyboardMarkup(row_width=2, resize_keyboard=True)
    markup.add(
        types.KeyboardButton("List Restaurants"),
        types.KeyboardButton("Add Restaurant"),
        types.KeyboardButton("Update Restaurant"),
        types.KeyboardButton("Delete Restaurant"),
        types.KeyboardButton("Back to Main Menu"),
    )

    maintenance_bot.send_message(
        user_id,
        "Restaurants Management. What would you like to do?",
        reply_markup=markup,
    )


@maintenance_bot.message_handler(
    func=lambda message: message.text == "List Restaurants"
)
def list_restaurants(message):
    """List all restaurants"""
    user_id = message.from_user.id

    if not is_authorized(user_id):
        return

    restaurants = get_all_restaurants()

    if not restaurants:
        maintenance_bot.send_message(user_id, "No restaurants found.")
        return

    # Group restaurants by area
    restaurants_by_area = {}
    for restaurant in restaurants:
        area_id = restaurant["area_id"]
        if area_id not in restaurants_by_area:
            area = get_area_by_id(area_id)
            area_name = area["name"] if area else f"Unknown Area ({area_id})"
            restaurants_by_area[area_id] = {"name": area_name, "restaurants": []}

        restaurants_by_area[area_id]["restaurants"].append(restaurant)

    # Generate response text
    response = "Restaurants by Area:\n\n"
    for area_id, area_data in restaurants_by_area.items():
        response += f"Area: {area_data['name']}\n"
        for restaurant in area_data["restaurants"]:
            response += f"  ID: {restaurant['id']} - {restaurant['name']}\n"
        response += "\n"

    maintenance_bot.send_message(user_id, response)


@maintenance_bot.message_handler(func=lambda message: message.text == "Add Restaurant")
def ask_restaurant_area(message):
    """Ask for area ID to add a restaurant to"""
    user_id = message.from_user.id

    if not is_authorized(user_id):
        return

    areas = get_all_areas()

    if not areas:
        maintenance_bot.send_message(
            user_id, "No areas found. Please add an area first."
        )
        return

    areas_text = "Areas:\n\n"
    for area in areas:
        areas_text += f"ID: {area['id']} - {area['name']}\n"

    areas_text += "\nEnter the ID of the area for the new restaurant:"

    markup = types.ForceReply(selective=True)
    msg = maintenance_bot.send_message(user_id, areas_text, reply_markup=markup)

    user_states[user_id] = {"state": "adding_restaurant_area"}
    maintenance_bot.register_next_step_handler(msg, process_restaurant_area)


def process_restaurant_area(message):
    """Process area ID for adding a restaurant"""
    user_id = message.from_user.id

    if not is_authorized(user_id):
        return

    try:
        area_id = int(message.text.strip())
    except ValueError:
        maintenance_bot.send_message(user_id, "Please enter a valid ID (number).")
        return

    area = get_area_by_id(area_id)

    if not area:
        maintenance_bot.send_message(user_id, f"Area with ID {area_id} not found.")
        return

    user_states[user_id] = {"state": "adding_restaurant_name", "area_id": area_id}

    markup = types.ForceReply(selective=True)
    msg = maintenance_bot.send_message(
        user_id,
        f"Selected area: {area['name']}\nEnter the name of the new restaurant:",
        reply_markup=markup,
    )

    maintenance_bot.register_next_step_handler(msg, process_add_restaurant)


def process_add_restaurant(message):
    """Process adding a new restaurant"""
    user_id = message.from_user.id

    if not is_authorized(user_id):
        return

    restaurant_name = message.text.strip()

    if not restaurant_name:
        maintenance_bot.send_message(user_id, "Restaurant name cannot be empty.")
        return

    area_id = user_states[user_id].get("area_id")
    if not area_id:
        maintenance_bot.send_message(user_id, "Area ID not found.")
        return

    new_restaurant = add_restaurant(restaurant_name, area_id)

    if new_restaurant:
        area = get_area_by_id(area_id)
        area_name = area["name"] if area else f"Unknown Area ({area_id})"

        # Create a keyboard with navigation options
        markup = types.ReplyKeyboardMarkup(row_width=2, resize_keyboard=True)
        markup.add(
            types.KeyboardButton("Add Another Restaurant"),
            types.KeyboardButton("Back to Restaurants Menu"),
            types.KeyboardButton("Back to Main Menu"),
        )

        maintenance_bot.send_message(
            user_id,
            f"Restaurant added successfully!\nID: {new_restaurant['id']} - {new_restaurant['name']}\nArea: {area_name}",
            reply_markup=markup,
        )
    else:
        maintenance_bot.send_message(user_id, "Failed to add restaurant.")


@maintenance_bot.message_handler(
    func=lambda message: message.text == "Update Restaurant"
)
def ask_restaurant_id_to_update(message):
    """Ask for restaurant ID to update"""
    user_id = message.from_user.id

    if not is_authorized(user_id):
        return

    restaurants = get_all_restaurants()

    if not restaurants:
        maintenance_bot.send_message(user_id, "No restaurants found to update.")
        return

    # Group restaurants by area for better display
    restaurants_by_area = group_restaurants_by_area(restaurants)

    # Generate response text
    response = "Restaurants by Area:\n\n"
    for _, area_data in restaurants_by_area.items():
        response += f"Area: {area_data['name']}\n"
        for restaurant in area_data["restaurants"]:
            response += f"  ID: {restaurant['id']} - {restaurant['name']}\n"
        response += "\n"

    response += "Enter the ID of the restaurant you want to update:"

    markup = types.ForceReply(selective=True)
    msg = maintenance_bot.send_message(user_id, response, reply_markup=markup)

    user_states[user_id] = {"state": "updating_restaurant_id"}
    maintenance_bot.register_next_step_handler(msg, process_restaurant_id_to_update)


def process_restaurant_id_to_update(message):
    """Process restaurant ID to update"""
    user_id = message.from_user.id

    if not is_authorized(user_id):
        return

    try:
        restaurant_id = int(message.text.strip())
    except ValueError:
        maintenance_bot.send_message(user_id, "Please enter a valid ID (number).")
        return

    restaurant = get_restaurant_by_id(restaurant_id)

    if not restaurant:
        maintenance_bot.send_message(
            user_id, f"Restaurant with ID {restaurant_id} not found."
        )
        return

    # Ask what to update: name or area
    user_states[user_id] = {
        "state": "updating_restaurant",
        "restaurant_id": restaurant_id,
    }

    markup = types.ReplyKeyboardMarkup(row_width=2, resize_keyboard=True)
    markup.add(
        types.KeyboardButton("Update Name"),
        types.KeyboardButton("Update Area"),
        types.KeyboardButton("Back"),
    )

    area = get_area_by_id(restaurant["area_id"])
    area_name = area["name"] if area else f"Unknown Area ({restaurant['area_id']})"

    maintenance_bot.send_message(
        user_id,
        f"Restaurant ID: {restaurant['id']}\nCurrent Name: {restaurant['name']}\nCurrent Area: {area_name}\n\nWhat would you like to update?",
        reply_markup=markup,
    )


@maintenance_bot.message_handler(
    func=lambda message: message.text == "Update Name"
    and user_states.get(message.from_user.id, {}).get("state") == "updating_restaurant"
)
def ask_restaurant_new_name(message):
    """Ask for new restaurant name"""
    user_id = message.from_user.id

    if not is_authorized(user_id):
        return

    restaurant_id = user_states[user_id].get("restaurant_id")
    if not restaurant_id:
        maintenance_bot.send_message(user_id, "Restaurant ID not found.")
        show_restaurants_menu(message)
        return

    restaurant = get_restaurant_by_id(restaurant_id)
    if not restaurant:
        maintenance_bot.send_message(
            user_id, f"Restaurant with ID {restaurant_id} not found."
        )
        show_restaurants_menu(message)
        return

    markup = types.ForceReply(selective=True)
    msg = maintenance_bot.send_message(
        user_id,
        f"Current name: {restaurant['name']}\nEnter the new name for the restaurant:",
        reply_markup=markup,
    )

    user_states[user_id] = {
        "state": "updating_restaurant_name",
        "restaurant_id": restaurant_id,
    }
    maintenance_bot.register_next_step_handler(msg, process_update_restaurant_name)


@maintenance_bot.message_handler(
    func=lambda message: message.text == "Update Area"
    and user_states.get(message.from_user.id, {}).get("state") == "updating_restaurant"
)
def ask_restaurant_new_area(message):
    """Ask for new restaurant area"""
    user_id = message.from_user.id

    if not is_authorized(user_id):
        return

    restaurant_id = user_states[user_id].get("restaurant_id")
    if not restaurant_id:
        maintenance_bot.send_message(user_id, "Restaurant ID not found.")
        show_restaurants_menu(message)
        return

    restaurant = get_restaurant_by_id(restaurant_id)
    if not restaurant:
        maintenance_bot.send_message(
            user_id, f"Restaurant with ID {restaurant_id} not found."
        )
        show_restaurants_menu(message)
        return

    # Get all areas to show to the user
    areas = get_all_areas()
    if not areas:
        maintenance_bot.send_message(
            user_id, "No areas found. Please add an area first."
        )
        show_restaurants_menu(message)
        return

    areas_text = "Areas:\n\n"
    for area in areas:
        areas_text += f"ID: {area['id']} - {area['name']}\n"

    areas_text += "\nEnter the ID of the area you want to move this restaurant to:"

    markup = types.ForceReply(selective=True)
    msg = maintenance_bot.send_message(user_id, areas_text, reply_markup=markup)

    user_states[user_id] = {
        "state": "updating_restaurant_area",
        "restaurant_id": restaurant_id,
    }
    maintenance_bot.register_next_step_handler(msg, process_update_restaurant_area)


def process_update_restaurant_name(message):
    """Process updating restaurant name"""
    user_id = message.from_user.id

    if not is_authorized(user_id):
        return

    restaurant_name = message.text.strip()

    if not restaurant_name:
        maintenance_bot.send_message(user_id, "Restaurant name cannot be empty.")
        return

    restaurant_id = user_states[user_id].get("restaurant_id")
    if not restaurant_id:
        maintenance_bot.send_message(user_id, "Restaurant ID not found.")
        return

    updated_restaurant = update_restaurant(restaurant_id, name=restaurant_name)

    if updated_restaurant:
        area = get_area_by_id(updated_restaurant["area_id"])
        area_name = (
            area["name"] if area else f"Unknown Area ({updated_restaurant['area_id']})"
        )

        # Create a keyboard with navigation options
        markup = types.ReplyKeyboardMarkup(row_width=2, resize_keyboard=True)
        markup.add(
            types.KeyboardButton("Update Another Restaurant"),
            types.KeyboardButton("Back to Restaurants Menu"),
            types.KeyboardButton("Back to Main Menu"),
        )

        maintenance_bot.send_message(
            user_id,
            f"Restaurant name updated successfully!\nID: {updated_restaurant['id']} - {updated_restaurant['name']}\nArea: {area_name}",
            reply_markup=markup,
        )
    else:
        maintenance_bot.send_message(user_id, "Failed to update restaurant name.")


def process_update_restaurant_area(message):
    """Process updating restaurant area"""
    user_id = message.from_user.id

    if not is_authorized(user_id):
        return

    try:
        area_id = int(message.text.strip())
    except ValueError:
        maintenance_bot.send_message(user_id, "Please enter a valid ID (number).")
        return

    # Check if area exists
    area = get_area_by_id(area_id)
    if not area:
        maintenance_bot.send_message(user_id, f"Area with ID {area_id} not found.")
        return

    restaurant_id = user_states[user_id].get("restaurant_id")
    if not restaurant_id:
        maintenance_bot.send_message(user_id, "Restaurant ID not found.")
        return

    # Get the restaurant before update for comparison
    restaurant = get_restaurant_by_id(restaurant_id)
    if not restaurant:
        maintenance_bot.send_message(
            user_id, f"Restaurant with ID {restaurant_id} not found."
        )
        return

    # Update the restaurant's area
    updated_restaurant = update_restaurant(restaurant_id, area_id=area_id)

    if updated_restaurant:
        # Get the new area name for confirmation message
        new_area = get_area_by_id(area_id)
        new_area_name = new_area["name"] if new_area else f"Unknown Area ({area_id})"

        # Create a keyboard with navigation options
        markup = types.ReplyKeyboardMarkup(row_width=2, resize_keyboard=True)
        markup.add(
            types.KeyboardButton("Update Another Restaurant"),
            types.KeyboardButton("Back to Restaurants Menu"),
            types.KeyboardButton("Back to Main Menu"),
        )

        maintenance_bot.send_message(
            user_id,
            f"Restaurant area updated successfully!\nID: {updated_restaurant['id']} - {updated_restaurant['name']}\nNew Area: {new_area_name}",
            reply_markup=markup,
        )
    else:
        maintenance_bot.send_message(user_id, "Failed to update restaurant area.")


@maintenance_bot.message_handler(
    func=lambda message: message.text == "Back"
    and user_states.get(message.from_user.id, {}).get("state") == "updating_restaurant"
)
def back_from_restaurant_update(message):
    """Handle back button from restaurant update menu"""
    user_id = message.from_user.id

    if not is_authorized(user_id):
        return

    # Clear the state
    if user_id in user_states:
        user_states.pop(user_id)

    # Go back to restaurants menu
    show_restaurants_menu(message)


@maintenance_bot.message_handler(
    func=lambda message: message.text == "Back to Main Menu"
)
def back_to_main_menu(message):
    """Return to the main menu"""
    user_id = message.from_user.id

    if not is_authorized(user_id):
        return

    # Clear any state
    if user_id in user_states:
        user_states.pop(user_id)

    markup = types.ReplyKeyboardMarkup(row_width=2, resize_keyboard=True)
    markup.add(
        types.KeyboardButton("Areas"),
        types.KeyboardButton("Restaurants"),
        types.KeyboardButton("Menus"),
        types.KeyboardButton("Delivery Locations"),
        types.KeyboardButton("Delivery Fees"),
    )

    maintenance_bot.send_message(
        user_id, "Main Menu. What would you like to manage?", reply_markup=markup
    )


@maintenance_bot.message_handler(
    func=lambda message: message.text == "Add Another Restaurant"
)
def add_another_restaurant(message):
    """Handle add another restaurant button"""
    user_id = message.from_user.id

    if not is_authorized(user_id):
        return

    # Clear any state
    if user_id in user_states:
        user_states.pop(user_id)

    # Go to add restaurant flow
    ask_restaurant_area(message)


@maintenance_bot.message_handler(
    func=lambda message: message.text == "Back to Restaurants Menu"
)
def back_to_restaurants_menu(message):
    """Handle back to restaurants menu button"""
    user_id = message.from_user.id

    if not is_authorized(user_id):
        return

    # Clear any state
    if user_id in user_states:
        user_states.pop(user_id)

    # Go back to restaurants menu
    show_restaurants_menu(message)


@maintenance_bot.message_handler(
    func=lambda message: message.text == "Delete Restaurant"
)
def ask_restaurant_id_to_delete(message):
    """Ask for restaurant ID to delete"""
    user_id = message.from_user.id

    if not is_authorized(user_id):
        return

    restaurants = get_all_restaurants()

    if not restaurants:
        maintenance_bot.send_message(user_id, "No restaurants found to delete.")
        return

    # Group restaurants by area for better display
    restaurants_by_area = group_restaurants_by_area(restaurants)

    # Generate response text
    response = "Restaurants by Area:\n\n"
    for _, area_data in restaurants_by_area.items():
        response += f"Area: {area_data['name']}\n"
        for restaurant in area_data["restaurants"]:
            response += f"  ID: {restaurant['id']} - {restaurant['name']}\n"
        response += "\n"

    response += "Enter the ID of the restaurant you want to delete:"

    markup = types.ForceReply(selective=True)
    msg = maintenance_bot.send_message(user_id, response, reply_markup=markup)

    user_states[user_id] = {"state": "deleting_restaurant"}
    maintenance_bot.register_next_step_handler(msg, process_delete_restaurant)


def process_delete_restaurant(message):
    """Process deleting a restaurant"""
    user_id = message.from_user.id

    if not is_authorized(user_id):
        return

    try:
        restaurant_id = int(message.text.strip())
    except ValueError:
        maintenance_bot.send_message(user_id, "Please enter a valid ID (number).")
        return

    restaurant = get_restaurant_by_id(restaurant_id)

    if not restaurant:
        maintenance_bot.send_message(
            user_id, f"Restaurant with ID {restaurant_id} not found."
        )
        return

    # Get area name for confirmation message
    area = get_area_by_id(restaurant["area_id"])
    area_name = area["name"] if area else f"Unknown Area ({restaurant['area_id']})"

    # Confirm deletion
    markup = types.ReplyKeyboardMarkup(row_width=2, resize_keyboard=True)
    markup.add(
        types.KeyboardButton(f"Confirm Delete ID {restaurant_id}"),
        types.KeyboardButton("Cancel Delete"),
        types.KeyboardButton("Back to Restaurants Menu"),
        types.KeyboardButton("Back to Main Menu"),
    )

    maintenance_bot.send_message(
        user_id,
        f"Are you sure you want to delete this restaurant?\n\nID: {restaurant['id']}\nName: {restaurant['name']}\nArea: {area_name}\n\nThis action cannot be undone.",
        reply_markup=markup,
    )

    user_states[user_id] = {
        "state": "confirming_restaurant_delete",
        "restaurant_id": restaurant_id,
    }


@maintenance_bot.message_handler(
    func=lambda message: message.text
    and message.text.startswith("Confirm Delete ID ")
    and user_states.get(message.from_user.id, {}).get("state")
    == "confirming_restaurant_delete"
)
def confirm_delete_restaurant(message):
    """Confirm and process restaurant deletion"""
    user_id = message.from_user.id

    if not is_authorized(user_id):
        return

    restaurant_id = user_states[user_id].get("restaurant_id")
    if not restaurant_id:
        maintenance_bot.send_message(user_id, "Restaurant ID not found.")
        show_restaurants_menu(message)
        return

    # Get restaurant info before deletion for confirmation message
    restaurant = get_restaurant_by_id(restaurant_id)
    if not restaurant:
        maintenance_bot.send_message(
            user_id, f"Restaurant with ID {restaurant_id} not found."
        )
        show_restaurants_menu(message)
        return

    restaurant_name = restaurant["name"]
    area_id = restaurant["area_id"]
    area = get_area_by_id(area_id)
    area_name = area["name"] if area else f"Unknown Area ({area_id})"

    # Delete the restaurant
    success = delete_restaurant(restaurant_id)

    if success:
        # Create a keyboard with navigation options
        markup = types.ReplyKeyboardMarkup(row_width=2, resize_keyboard=True)
        markup.add(
            types.KeyboardButton("Delete Another Restaurant"),
            types.KeyboardButton("Back to Restaurants Menu"),
            types.KeyboardButton("Back to Main Menu"),
        )

        maintenance_bot.send_message(
            user_id,
            f"Restaurant deleted successfully!\nID: {restaurant_id} - {restaurant_name}\nArea: {area_name}",
            reply_markup=markup,
        )
    else:
        maintenance_bot.send_message(user_id, "Failed to delete restaurant.")
        show_restaurants_menu(message)


@maintenance_bot.message_handler(
    func=lambda message: message.text == "Cancel Delete"
    and user_states.get(message.from_user.id, {}).get("state")
    == "confirming_restaurant_delete"
)
def cancel_delete_restaurant(message):
    """Cancel restaurant deletion"""
    user_id = message.from_user.id

    if not is_authorized(user_id):
        return

    # Clear the state
    if user_id in user_states:
        user_states.pop(user_id)

    maintenance_bot.send_message(user_id, "Restaurant deletion cancelled.")
    show_restaurants_menu(message)


@maintenance_bot.message_handler(
    func=lambda message: message.text == "Delete Another Restaurant"
)
def delete_another_restaurant(message):
    """Handle delete another restaurant button"""
    user_id = message.from_user.id

    if not is_authorized(user_id):
        return

    # Clear any state
    if user_id in user_states:
        user_states.pop(user_id)

    # Go to delete restaurant flow
    ask_restaurant_id_to_delete(message)


@maintenance_bot.message_handler(
    func=lambda message: message.text == "Update Another Restaurant"
)
def update_another_restaurant(message):
    """Handle update another restaurant button"""
    user_id = message.from_user.id

    if not is_authorized(user_id):
        return

    # Clear any state
    if user_id in user_states:
        user_states.pop(user_id)

    # Go to update restaurant flow
    ask_restaurant_id_to_update(message)


@maintenance_bot.message_handler(
    func=lambda message: message.text == "Update Another Area"
)
def update_another_area(message):
    """Handle update another area button"""
    user_id = message.from_user.id

    if not is_authorized(user_id):
        return

    # Clear any state
    if user_id in user_states:
        user_states.pop(user_id)

    # Go to update area flow
    ask_area_id_to_update(message)


@maintenance_bot.message_handler(
    func=lambda message: message.text == "Back to Areas Menu"
)
def back_to_areas_menu(message):
    """Handle back to areas menu button"""
    user_id = message.from_user.id

    if not is_authorized(user_id):
        return

    # Clear any state
    if user_id in user_states:
        user_states.pop(user_id)

    # Go back to areas menu
    show_areas_menu(message)


# Menu Management
@maintenance_bot.message_handler(func=lambda message: message.text == "Menus")
def show_menus_menu(message):
    """Show the menus management menu"""
    user_id = message.from_user.id

    if not is_authorized(user_id):
        return

    markup = types.ReplyKeyboardMarkup(row_width=2, resize_keyboard=True)
    markup.add(
        types.KeyboardButton("List Restaurant Menus"),
        types.KeyboardButton("Add Menu Item"),
        types.KeyboardButton("Update Menu Item"),
        types.KeyboardButton("Delete Menu Item"),
        types.KeyboardButton("Back to Main Menu"),
    )

    maintenance_bot.send_message(
        user_id, "Menus Management. What would you like to do?", reply_markup=markup
    )


@maintenance_bot.message_handler(
    func=lambda message: message.text == "List Restaurant Menus"
)
def list_restaurant_menus(message):
    """List all restaurants to select one for viewing its menu"""
    user_id = message.from_user.id

    if not is_authorized(user_id):
        return

    restaurants = get_all_restaurants()

    if not restaurants:
        maintenance_bot.send_message(user_id, "No restaurants found.")
        return

    # Group restaurants by area for better display
    restaurants_by_area = {}
    for restaurant in restaurants:
        area_id = restaurant["area_id"]
        if area_id not in restaurants_by_area:
            area = get_area_by_id(area_id)
            area_name = area["name"] if area else f"Unknown Area ({area_id})"
            restaurants_by_area[area_id] = {"name": area_name, "restaurants": []}

        restaurants_by_area[area_id]["restaurants"].append(restaurant)

    # Generate response text
    response = "Select a restaurant to view its menu:\n\n"
    for area_id, area_data in restaurants_by_area.items():
        response += f"Area: {area_data['name']}\n"
        for restaurant in area_data["restaurants"]:
            response += f"  ID: {restaurant['id']} - {restaurant['name']}\n"
        response += "\n"

    response += "Enter the ID of the restaurant to view its menu:"

    markup = types.ForceReply(selective=True)
    msg = maintenance_bot.send_message(user_id, response, reply_markup=markup)

    user_states[user_id] = {"state": "viewing_restaurant_menu"}
    maintenance_bot.register_next_step_handler(msg, process_view_restaurant_menu)


def process_view_restaurant_menu(message):
    """Process viewing a restaurant's menu"""
    user_id = message.from_user.id

    if not is_authorized(user_id):
        return

    try:
        restaurant_id = int(message.text.strip())
    except ValueError:
        maintenance_bot.send_message(user_id, "Please enter a valid ID (number).")
        return

    restaurant = get_restaurant_by_id(restaurant_id)

    if not restaurant:
        maintenance_bot.send_message(
            user_id, f"Restaurant with ID {restaurant_id} not found."
        )
        return

    # Get the restaurant's menu
    menu_items = get_restaurant_menu(restaurant_id)

    # Get area name for display
    area = get_area_by_id(restaurant["area_id"])
    area_name = area["name"] if area else f"Unknown Area ({restaurant['area_id']})"

    # Create a keyboard with navigation options
    markup = types.ReplyKeyboardMarkup(row_width=2, resize_keyboard=True)
    markup.add(
        types.KeyboardButton(f"Add Item to Menu {restaurant_id}"),
        types.KeyboardButton("View Another Menu"),
        types.KeyboardButton("Back to Menus Menu"),
        types.KeyboardButton("Back to Main Menu"),
    )

    if not menu_items:
        maintenance_bot.send_message(
            user_id,
            f"Menu for {restaurant['name']} (Area: {area_name}):\n\nNo menu items found.",
            reply_markup=markup,
        )
        return

    # Generate menu text
    menu_text = f"Menu for {restaurant['name']} (Area: {area_name}):\n\n"
    for item in menu_items:
        menu_text += f"ID: {item['id']} - {item['name']} - Price: {item['price']} ETB\n"

    # Store the restaurant ID for potential add/update/delete operations
    user_states[user_id] = {"state": "viewing_menu", "restaurant_id": restaurant_id}

    maintenance_bot.send_message(user_id, menu_text, reply_markup=markup)


@maintenance_bot.message_handler(
    func=lambda message: message.text == "View Another Menu"
)
def view_another_menu(message):
    """Handle view another menu button"""
    user_id = message.from_user.id

    if not is_authorized(user_id):
        return

    # Clear any state
    if user_id in user_states:
        user_states.pop(user_id)

    # Go to list restaurant menus flow
    list_restaurant_menus(message)


@maintenance_bot.message_handler(
    func=lambda message: message.text == "Back to Menus Menu"
)
def back_to_menus_menu(message):
    """Handle back to menus menu button"""
    user_id = message.from_user.id

    if not is_authorized(user_id):
        return

    # Clear any state
    if user_id in user_states:
        user_states.pop(user_id)

    # Go back to menus menu
    show_menus_menu(message)


@maintenance_bot.message_handler(
    func=lambda message: message.text == "Add Menu Item"
    or message.text
    and message.text.startswith("Add Item to Menu ")
)
def ask_restaurant_for_menu_item(message):
    """Ask for restaurant ID to add a menu item to"""
    user_id = message.from_user.id

    if not is_authorized(user_id):
        return

    # Check if we already have a restaurant ID from the button
    if message.text.startswith("Add Item to Menu "):
        try:
            restaurant_id = int(message.text.replace("Add Item to Menu ", "").strip())
            restaurant = get_restaurant_by_id(restaurant_id)
            if restaurant:
                user_states[user_id] = {
                    "state": "adding_menu_item_name",
                    "restaurant_id": restaurant_id,
                }
                markup = types.ForceReply(selective=True)
                msg = maintenance_bot.send_message(
                    user_id,
                    f"Adding menu item to {restaurant['name']}\nEnter the name of the menu item:",
                    reply_markup=markup,
                )
                maintenance_bot.register_next_step_handler(msg, process_menu_item_name)
                return
        except ValueError:
            pass

    # If we don't have a valid restaurant ID, ask for one
    restaurants = get_all_restaurants()

    if not restaurants:
        maintenance_bot.send_message(
            user_id, "No restaurants found. Please add a restaurant first."
        )
        return

    # Group restaurants by area for better display
    restaurants_by_area = group_restaurants_by_area(restaurants)

    # Generate response text
    response = "Select a restaurant to add a menu item to:\n\n"
    for _, area_data in restaurants_by_area.items():
        response += f"Area: {area_data['name']}\n"
        for restaurant in area_data["restaurants"]:
            response += f"  ID: {restaurant['id']} - {restaurant['name']}\n"
        response += "\n"

    response += "Enter the ID of the restaurant:"

    markup = types.ForceReply(selective=True)
    msg = maintenance_bot.send_message(user_id, response, reply_markup=markup)

    user_states[user_id] = {"state": "selecting_restaurant_for_menu_item"}
    maintenance_bot.register_next_step_handler(msg, process_restaurant_for_menu_item)


def process_restaurant_for_menu_item(message):
    """Process restaurant ID for adding a menu item"""
    user_id = message.from_user.id

    if not is_authorized(user_id):
        return

    try:
        restaurant_id = int(message.text.strip())
    except ValueError:
        maintenance_bot.send_message(user_id, "Please enter a valid ID (number).")
        return

    restaurant = get_restaurant_by_id(restaurant_id)

    if not restaurant:
        maintenance_bot.send_message(
            user_id, f"Restaurant with ID {restaurant_id} not found."
        )
        return

    user_states[user_id] = {
        "state": "adding_menu_item_name",
        "restaurant_id": restaurant_id,
    }

    markup = types.ForceReply(selective=True)
    msg = maintenance_bot.send_message(
        user_id,
        f"Adding menu item to {restaurant['name']}\nEnter the name of the menu item:",
        reply_markup=markup,
    )

    maintenance_bot.register_next_step_handler(msg, process_menu_item_name)


def process_menu_item_name(message):
    """Process menu item name"""
    user_id = message.from_user.id

    if not is_authorized(user_id):
        return

    menu_item_name = message.text.strip()

    if not menu_item_name:
        maintenance_bot.send_message(user_id, "Menu item name cannot be empty.")
        return

    restaurant_id = user_states[user_id].get("restaurant_id")
    if not restaurant_id:
        maintenance_bot.send_message(user_id, "Restaurant ID not found.")
        return

    # Store the menu item name and ask for price
    user_states[user_id] = {
        "state": "adding_menu_item_price",
        "restaurant_id": restaurant_id,
        "menu_item_name": menu_item_name,
    }

    markup = types.ForceReply(selective=True)
    msg = maintenance_bot.send_message(
        user_id,
        f"Enter the price for '{menu_item_name}' (in ETB):",
        reply_markup=markup,
    )

    maintenance_bot.register_next_step_handler(msg, process_menu_item_price)


def process_menu_item_price(message):
    """Process menu item price"""
    user_id = message.from_user.id

    if not is_authorized(user_id):
        return

    try:
        # Convert price to int instead of float
        price = int(float(message.text.strip()))
    except ValueError:
        maintenance_bot.send_message(user_id, "Please enter a valid price (number).")
        return

    restaurant_id = user_states[user_id].get("restaurant_id")
    menu_item_name = user_states[user_id].get("menu_item_name")

    if not restaurant_id or not menu_item_name:
        maintenance_bot.send_message(
            user_id, "Restaurant ID or menu item name not found."
        )
        return

    # Add the menu item
    new_item = add_menu_item(restaurant_id, menu_item_name, price)

    if new_item:
        restaurant = get_restaurant_by_id(restaurant_id)
        restaurant_name = (
            restaurant["name"] if restaurant else f"Restaurant ID {restaurant_id}"
        )

        # Create a keyboard with navigation options
        markup = types.ReplyKeyboardMarkup(row_width=2, resize_keyboard=True)
        markup.add(
            types.KeyboardButton(f"Add Another Item to Menu {restaurant_id}"),
            types.KeyboardButton("View Menu"),
            types.KeyboardButton("Back to Menus Menu"),
            types.KeyboardButton("Back to Main Menu"),
        )

        maintenance_bot.send_message(
            user_id,
            f"Menu item added successfully!\nRestaurant: {restaurant_name}\nItem: {new_item['name']}\nPrice: {new_item['price']} ETB",
            reply_markup=markup,
        )

        # Update the state to include the restaurant ID for potential further operations
        user_states[user_id] = {
            "state": "menu_item_added",
            "restaurant_id": restaurant_id,
        }
    else:
        maintenance_bot.send_message(user_id, "Failed to add menu item.")


@maintenance_bot.message_handler(
    func=lambda message: message.text
    and message.text.startswith("Add Another Item to Menu ")
)
def add_another_menu_item(message):
    """Handle add another menu item button"""
    user_id = message.from_user.id

    if not is_authorized(user_id):
        return

    try:
        restaurant_id = int(
            message.text.replace("Add Another Item to Menu ", "").strip()
        )
        restaurant = get_restaurant_by_id(restaurant_id)

        if restaurant:
            user_states[user_id] = {
                "state": "adding_menu_item_name",
                "restaurant_id": restaurant_id,
            }

            markup = types.ForceReply(selective=True)
            msg = maintenance_bot.send_message(
                user_id,
                f"Adding another menu item to {restaurant['name']}\nEnter the name of the menu item:",
                reply_markup=markup,
            )

            maintenance_bot.register_next_step_handler(msg, process_menu_item_name)
        else:
            maintenance_bot.send_message(
                user_id, f"Restaurant with ID {restaurant_id} not found."
            )
            show_menus_menu(message)
    except ValueError:
        maintenance_bot.send_message(user_id, "Invalid restaurant ID.")
        show_menus_menu(message)


@maintenance_bot.message_handler(
    func=lambda message: message.text == "View Menu"
    and user_states.get(message.from_user.id, {}).get("state") == "menu_item_added"
)
def view_current_menu(message):
    """View the menu of the restaurant we just added an item to"""
    user_id = message.from_user.id

    if not is_authorized(user_id):
        return

    restaurant_id = user_states[user_id].get("restaurant_id")
    if not restaurant_id:
        maintenance_bot.send_message(user_id, "Restaurant ID not found.")
        show_menus_menu(message)
        return

    # Simulate the process_view_restaurant_menu function with the stored restaurant ID
    restaurant = get_restaurant_by_id(restaurant_id)
    if not restaurant:
        maintenance_bot.send_message(
            user_id, f"Restaurant with ID {restaurant_id} not found."
        )
        show_menus_menu(message)
        return

    # Get the restaurant's menu
    menu_items = get_restaurant_menu(restaurant_id)

    # Get area name for display
    area = get_area_by_id(restaurant["area_id"])
    area_name = area["name"] if area else f"Unknown Area ({restaurant['area_id']})"

    # Create a keyboard with navigation options
    markup = types.ReplyKeyboardMarkup(row_width=2, resize_keyboard=True)
    markup.add(
        types.KeyboardButton(f"Add Item to Menu {restaurant_id}"),
        types.KeyboardButton("View Another Menu"),
        types.KeyboardButton("Back to Menus Menu"),
        types.KeyboardButton("Back to Main Menu"),
    )

    if not menu_items:
        maintenance_bot.send_message(
            user_id,
            f"Menu for {restaurant['name']} (Area: {area_name}):\n\nNo menu items found.",
            reply_markup=markup,
        )
        return

    # Generate menu text
    menu_text = f"Menu for {restaurant['name']} (Area: {area_name}):\n\n"
    for item in menu_items:
        menu_text += f"ID: {item['id']} - {item['name']} - Price: {item['price']} ETB\n"

    # Store the restaurant ID for potential add/update/delete operations
    user_states[user_id] = {"state": "viewing_menu", "restaurant_id": restaurant_id}

    maintenance_bot.send_message(user_id, menu_text, reply_markup=markup)


@maintenance_bot.message_handler(
    func=lambda message: message.text == "Update Menu Item"
)
def ask_restaurant_for_menu_item_update(message):
    """Ask for restaurant ID to update a menu item"""
    user_id = message.from_user.id

    if not is_authorized(user_id):
        return

    restaurants = get_all_restaurants()

    if not restaurants:
        maintenance_bot.send_message(user_id, "No restaurants found.")
        return

    # Group restaurants by area for better display
    restaurants_by_area = group_restaurants_by_area(restaurants)

    # Generate response text
    response = "Select a restaurant to update a menu item:\n\n"
    for _, area_data in restaurants_by_area.items():
        response += f"Area: {area_data['name']}\n"
        for restaurant in area_data["restaurants"]:
            response += f"  ID: {restaurant['id']} - {restaurant['name']}\n"
        response += "\n"

    response += "Enter the ID of the restaurant:"

    markup = types.ForceReply(selective=True)
    msg = maintenance_bot.send_message(user_id, response, reply_markup=markup)

    user_states[user_id] = {"state": "selecting_restaurant_for_menu_update"}
    maintenance_bot.register_next_step_handler(msg, process_restaurant_for_menu_update)


def process_restaurant_for_menu_update(message):
    """Process restaurant ID for updating a menu item"""
    user_id = message.from_user.id

    if not is_authorized(user_id):
        return

    try:
        restaurant_id = int(message.text.strip())
    except ValueError:
        maintenance_bot.send_message(user_id, "Please enter a valid ID (number).")
        return

    restaurant = get_restaurant_by_id(restaurant_id)

    if not restaurant:
        maintenance_bot.send_message(
            user_id, f"Restaurant with ID {restaurant_id} not found."
        )
        return

    # Get the restaurant's menu
    menu_items = get_restaurant_menu(restaurant_id)

    if not menu_items:
        maintenance_bot.send_message(
            user_id,
            f"No menu items found for {restaurant['name']}. Please add menu items first.",
        )
        return

    # Generate menu text
    menu_text = f"Menu items for {restaurant['name']}:\n\n"
    for item in menu_items:
        menu_text += f"ID: {item['id']} - {item['name']} - Price: {item['price']} ETB\n"

    menu_text += "\nEnter the ID of the menu item you want to update:"

    markup = types.ForceReply(selective=True)
    msg = maintenance_bot.send_message(user_id, menu_text, reply_markup=markup)

    user_states[user_id] = {
        "state": "selecting_menu_item_to_update",
        "restaurant_id": restaurant_id,
    }
    maintenance_bot.register_next_step_handler(msg, process_menu_item_to_update)


def process_menu_item_to_update(message):
    """Process menu item ID for updating"""
    user_id = message.from_user.id

    if not is_authorized(user_id):
        return

    try:
        menu_item_id = int(message.text.strip())
    except ValueError:
        maintenance_bot.send_message(user_id, "Please enter a valid ID (number).")
        return

    restaurant_id = user_states[user_id].get("restaurant_id")
    if not restaurant_id:
        maintenance_bot.send_message(user_id, "Restaurant ID not found.")
        return

    # Get the restaurant's menu
    menu_items = get_restaurant_menu(restaurant_id)

    # Find the menu item
    menu_item = None
    for item in menu_items:
        if item["id"] == menu_item_id:
            menu_item = item
            break

    if not menu_item:
        maintenance_bot.send_message(
            user_id, f"Menu item with ID {menu_item_id} not found."
        )
        return

    # Ask what to update
    markup = types.ReplyKeyboardMarkup(row_width=2, resize_keyboard=True)
    markup.add(
        types.KeyboardButton("Update Name"),
        types.KeyboardButton("Update Price"),
        types.KeyboardButton("Back to Menus Menu"),
        types.KeyboardButton("Back to Main Menu"),
    )

    maintenance_bot.send_message(
        user_id,
        f"Menu Item ID: {menu_item['id']}\nCurrent Name: {menu_item['name']}\nCurrent Price: {menu_item['price']} ETB\n\nWhat would you like to update?",
        reply_markup=markup,
    )

    user_states[user_id] = {
        "state": "updating_menu_item",
        "restaurant_id": restaurant_id,
        "menu_item_id": menu_item_id,
    }


@maintenance_bot.message_handler(
    func=lambda message: message.text == "Update Name"
    and user_states.get(message.from_user.id, {}).get("state") == "updating_menu_item"
)
def ask_menu_item_new_name(message):
    """Ask for new menu item name"""
    user_id = message.from_user.id

    if not is_authorized(user_id):
        return

    restaurant_id = user_states[user_id].get("restaurant_id")
    menu_item_id = user_states[user_id].get("menu_item_id")

    if not restaurant_id or not menu_item_id:
        maintenance_bot.send_message(
            user_id, "Restaurant ID or menu item ID not found."
        )
        show_menus_menu(message)
        return

    # Find the menu item
    menu_items = get_restaurant_menu(restaurant_id)
    menu_item = None
    for item in menu_items:
        if item["id"] == menu_item_id:
            menu_item = item
            break

    if not menu_item:
        maintenance_bot.send_message(
            user_id, f"Menu item with ID {menu_item_id} not found."
        )
        show_menus_menu(message)
        return

    markup = types.ForceReply(selective=True)
    msg = maintenance_bot.send_message(
        user_id,
        f"Current name: {menu_item['name']}\nEnter the new name for the menu item:",
        reply_markup=markup,
    )

    user_states[user_id] = {
        "state": "updating_menu_item_name",
        "restaurant_id": restaurant_id,
        "menu_item_id": menu_item_id,
    }
    maintenance_bot.register_next_step_handler(msg, process_update_menu_item_name)


def process_update_menu_item_name(message):
    """Process updating menu item name"""
    user_id = message.from_user.id

    if not is_authorized(user_id):
        return

    new_name = message.text.strip()

    if not new_name:
        maintenance_bot.send_message(user_id, "Menu item name cannot be empty.")
        return

    restaurant_id = user_states[user_id].get("restaurant_id")
    menu_item_id = user_states[user_id].get("menu_item_id")

    if not restaurant_id or not menu_item_id:
        maintenance_bot.send_message(
            user_id, "Restaurant ID or menu item ID not found."
        )
        return

    # Update the menu item
    updated_item = update_menu_item(restaurant_id, menu_item_id, name=new_name)

    if updated_item:
        restaurant = get_restaurant_by_id(restaurant_id)
        restaurant_name = (
            restaurant["name"] if restaurant else f"Restaurant ID {restaurant_id}"
        )

        # Create a keyboard with navigation options
        markup = types.ReplyKeyboardMarkup(row_width=2, resize_keyboard=True)
        markup.add(
            types.KeyboardButton("Update Another Menu Item"),
            types.KeyboardButton("View Menu"),
            types.KeyboardButton("Back to Menus Menu"),
            types.KeyboardButton("Back to Main Menu"),
        )

        maintenance_bot.send_message(
            user_id,
            f"Menu item name updated successfully!\nRestaurant: {restaurant_name}\nItem ID: {updated_item['id']}\nNew Name: {updated_item['name']}\nPrice: {updated_item['price']} ETB",
            reply_markup=markup,
        )

        # Update the state for potential further operations
        user_states[user_id] = {
            "state": "menu_item_updated",
            "restaurant_id": restaurant_id,
        }
    else:
        maintenance_bot.send_message(user_id, "Failed to update menu item name.")


@maintenance_bot.message_handler(
    func=lambda message: message.text == "Update Price"
    and user_states.get(message.from_user.id, {}).get("state") == "updating_menu_item"
)
def ask_menu_item_new_price(message):
    """Ask for new menu item price"""
    user_id = message.from_user.id

    if not is_authorized(user_id):
        return

    restaurant_id = user_states[user_id].get("restaurant_id")
    menu_item_id = user_states[user_id].get("menu_item_id")

    if not restaurant_id or not menu_item_id:
        maintenance_bot.send_message(
            user_id, "Restaurant ID or menu item ID not found."
        )
        show_menus_menu(message)
        return

    # Find the menu item
    menu_items = get_restaurant_menu(restaurant_id)
    menu_item = None
    for item in menu_items:
        if item["id"] == menu_item_id:
            menu_item = item
            break

    if not menu_item:
        maintenance_bot.send_message(
            user_id, f"Menu item with ID {menu_item_id} not found."
        )
        show_menus_menu(message)
        return

    markup = types.ForceReply(selective=True)
    msg = maintenance_bot.send_message(
        user_id,
        f"Current price: {menu_item['price']} ETB\nEnter the new price for the menu item (in ETB):",
        reply_markup=markup,
    )

    user_states[user_id] = {
        "state": "updating_menu_item_price",
        "restaurant_id": restaurant_id,
        "menu_item_id": menu_item_id,
    }
    maintenance_bot.register_next_step_handler(msg, process_update_menu_item_price)


def process_update_menu_item_price(message):
    """Process updating menu item price"""
    user_id = message.from_user.id

    if not is_authorized(user_id):
        return

    try:
        # Convert price to int instead of float
        new_price = int(float(message.text.strip()))
    except ValueError:
        maintenance_bot.send_message(user_id, "Please enter a valid price (number).")
        return

    restaurant_id = user_states[user_id].get("restaurant_id")
    menu_item_id = user_states[user_id].get("menu_item_id")

    if not restaurant_id or not menu_item_id:
        maintenance_bot.send_message(
            user_id, "Restaurant ID or menu item ID not found."
        )
        return

    # Enhanced logging for debugging deployment issues
    logger.info(f"Attempting to update menu item - Restaurant ID: {restaurant_id}, Item ID: {menu_item_id}, New Price: {new_price}")

    # Check Firebase connectivity before attempting update
    from src.firebase_db import test_firebase_connectivity
    if not test_firebase_connectivity():
        logger.error("Firebase connectivity test failed before menu item update")
        maintenance_bot.send_message(
            user_id,
            "❌ Database connection error. Please try again in a moment or contact support if the issue persists."
        )
        return

    # Update the menu item with enhanced error handling
    try:
        updated_item = update_menu_item(restaurant_id, menu_item_id, price=new_price)

        if updated_item:
            logger.info(f"Successfully updated menu item {menu_item_id} to price {new_price}")
            restaurant = get_restaurant_by_id(restaurant_id)
            restaurant_name = (
                restaurant["name"] if restaurant else f"Restaurant ID {restaurant_id}"
            )

            # Create a keyboard with navigation options
            markup = types.ReplyKeyboardMarkup(row_width=2, resize_keyboard=True)
            markup.add(
                types.KeyboardButton("Update Another Menu Item"),
                types.KeyboardButton("View Menu"),
                types.KeyboardButton("Back to Menus Menu"),
                types.KeyboardButton("Back to Main Menu"),
            )

            maintenance_bot.send_message(
                user_id,
                f"✅ Menu item price updated successfully!\n\n"
                f"🏪 Restaurant: {restaurant_name}\n"
                f"🆔 Item ID: {updated_item['id']}\n"
                f"📝 Name: {updated_item['name']}\n"
                f"💰 New Price: {updated_item['price']} ETB",
                reply_markup=markup,
            )

            # Update the state for potential further operations
            user_states[user_id] = {
                "state": "menu_item_updated",
                "restaurant_id": restaurant_id,
            }
        else:
            logger.error(f"update_menu_item returned None for restaurant {restaurant_id}, item {menu_item_id}")
            maintenance_bot.send_message(
                user_id,
                "❌ Failed to update menu item price. The item may not exist or there was a database error.\n\n"
                "Please verify the item exists and try again, or contact support if the issue persists."
            )

    except Exception as e:
        logger.error(f"Exception during menu item update: {e}", exc_info=True)
        maintenance_bot.send_message(
            user_id,
            "❌ An error occurred while updating the menu item price. Please try again or contact support."
        )


@maintenance_bot.message_handler(
    func=lambda message: message.text == "Update Another Menu Item"
)
def update_another_menu_item(message):
    """Handle update another menu item button"""
    user_id = message.from_user.id

    if not is_authorized(user_id):
        return

    # Clear any state
    if user_id in user_states:
        user_states.pop(user_id)

    # Go to update menu item flow
    ask_restaurant_for_menu_item_update(message)


@maintenance_bot.message_handler(
    func=lambda message: message.text == "Delete Menu Item"
)
def ask_restaurant_for_menu_item_delete(message):
    """Ask for restaurant ID to delete a menu item"""
    user_id = message.from_user.id

    if not is_authorized(user_id):
        return

    restaurants = get_all_restaurants()

    if not restaurants:
        maintenance_bot.send_message(user_id, "No restaurants found.")
        return

    # Group restaurants by area for better display
    restaurants_by_area = group_restaurants_by_area(restaurants)

    # Generate response text
    response = "Select a restaurant to delete a menu item from:\n\n"
    for _, area_data in restaurants_by_area.items():
        response += f"Area: {area_data['name']}\n"
        for restaurant in area_data["restaurants"]:
            response += f"  ID: {restaurant['id']} - {restaurant['name']}\n"
        response += "\n"

    response += "Enter the ID of the restaurant:"

    markup = types.ForceReply(selective=True)
    msg = maintenance_bot.send_message(user_id, response, reply_markup=markup)

    user_states[user_id] = {"state": "selecting_restaurant_for_menu_delete"}
    maintenance_bot.register_next_step_handler(msg, process_restaurant_for_menu_delete)


def process_restaurant_for_menu_delete(message):
    """Process restaurant ID for deleting a menu item"""
    user_id = message.from_user.id

    if not is_authorized(user_id):
        return

    try:
        restaurant_id = int(message.text.strip())
    except ValueError:
        maintenance_bot.send_message(user_id, "Please enter a valid ID (number).")
        return

    restaurant = get_restaurant_by_id(restaurant_id)

    if not restaurant:
        maintenance_bot.send_message(
            user_id, f"Restaurant with ID {restaurant_id} not found."
        )
        return

    # Get the restaurant's menu
    menu_items = get_restaurant_menu(restaurant_id)

    if not menu_items:
        maintenance_bot.send_message(
            user_id,
            f"No menu items found for {restaurant['name']}. Please add menu items first.",
        )
        return

    # Generate menu text
    menu_text = f"Menu items for {restaurant['name']}:\n\n"
    for item in menu_items:
        menu_text += f"ID: {item['id']} - {item['name']} - Price: {item['price']} ETB\n"

    menu_text += "\nEnter the ID of the menu item you want to delete:"

    markup = types.ForceReply(selective=True)
    msg = maintenance_bot.send_message(user_id, menu_text, reply_markup=markup)

    user_states[user_id] = {
        "state": "selecting_menu_item_to_delete",
        "restaurant_id": restaurant_id,
    }
    maintenance_bot.register_next_step_handler(msg, process_menu_item_to_delete)


def process_menu_item_to_delete(message):
    """Process menu item ID for deleting"""
    user_id = message.from_user.id

    if not is_authorized(user_id):
        return

    try:
        menu_item_id = int(message.text.strip())
    except ValueError:
        maintenance_bot.send_message(user_id, "Please enter a valid ID (number).")
        return

    restaurant_id = user_states[user_id].get("restaurant_id")
    if not restaurant_id:
        maintenance_bot.send_message(user_id, "Restaurant ID not found.")
        return

    # Find the menu item
    menu_items = get_restaurant_menu(restaurant_id)
    menu_item = None
    for item in menu_items:
        if item["id"] == menu_item_id:
            menu_item = item
            break

    if not menu_item:
        maintenance_bot.send_message(
            user_id, f"Menu item with ID {menu_item_id} not found."
        )
        return

    # Confirm deletion
    markup = types.ReplyKeyboardMarkup(row_width=2, resize_keyboard=True)
    markup.add(
        types.KeyboardButton(f"Confirm Delete Menu Item {menu_item_id}"),
        types.KeyboardButton("Cancel Delete"),
        types.KeyboardButton("Back to Menus Menu"),
        types.KeyboardButton("Back to Main Menu"),
    )

    maintenance_bot.send_message(
        user_id,
        f"Are you sure you want to delete this menu item?\n\nID: {menu_item['id']}\nName: {menu_item['name']}\nPrice: {menu_item['price']} ETB\n\nThis action cannot be undone.",
        reply_markup=markup,
    )

    user_states[user_id] = {
        "state": "confirming_menu_item_delete",
        "restaurant_id": restaurant_id,
        "menu_item_id": menu_item_id,
    }


@maintenance_bot.message_handler(
    func=lambda message: message.text
    and message.text.startswith("Confirm Delete Menu Item ")
    and user_states.get(message.from_user.id, {}).get("state")
    == "confirming_menu_item_delete"
)
def confirm_delete_menu_item(message):
    """Confirm and process menu item deletion"""
    user_id = message.from_user.id

    if not is_authorized(user_id):
        return

    restaurant_id = user_states[user_id].get("restaurant_id")
    menu_item_id = user_states[user_id].get("menu_item_id")

    if not restaurant_id or not menu_item_id:
        maintenance_bot.send_message(
            user_id, "Restaurant ID or menu item ID not found."
        )
        show_menus_menu(message)
        return

    # Find the menu item for confirmation message
    menu_items = get_restaurant_menu(restaurant_id)
    menu_item = None
    for item in menu_items:
        if item["id"] == menu_item_id:
            menu_item = item
            break

    if not menu_item:
        maintenance_bot.send_message(
            user_id, f"Menu item with ID {menu_item_id} not found."
        )
        show_menus_menu(message)
        return

    # Store item info before deletion
    item_name = menu_item["name"]
    item_price = menu_item["price"]

    # Delete the menu item
    success = delete_menu_item(restaurant_id, menu_item_id)

    if success:
        restaurant = get_restaurant_by_id(restaurant_id)
        restaurant_name = (
            restaurant["name"] if restaurant else f"Restaurant ID {restaurant_id}"
        )

        # Create a keyboard with navigation options
        markup = types.ReplyKeyboardMarkup(row_width=2, resize_keyboard=True)
        markup.add(
            types.KeyboardButton("Delete Another Menu Item"),
            types.KeyboardButton("View Menu"),
            types.KeyboardButton("Back to Menus Menu"),
            types.KeyboardButton("Back to Main Menu"),
        )

        maintenance_bot.send_message(
            user_id,
            f"Menu item deleted successfully!\nRestaurant: {restaurant_name}\nItem ID: {menu_item_id}\nName: {item_name}\nPrice: {item_price} ETB",
            reply_markup=markup,
        )

        # Update the state for potential further operations
        user_states[user_id] = {
            "state": "menu_item_deleted",
            "restaurant_id": restaurant_id,
        }
    else:
        maintenance_bot.send_message(user_id, "Failed to delete menu item.")
        show_menus_menu(message)


@maintenance_bot.message_handler(
    func=lambda message: message.text == "Cancel Delete"
    and user_states.get(message.from_user.id, {}).get("state")
    == "confirming_menu_item_delete"
)
def cancel_delete_menu_item(message):
    """Cancel menu item deletion"""
    user_id = message.from_user.id

    if not is_authorized(user_id):
        return

    # Clear the state
    if user_id in user_states:
        user_states.pop(user_id)

    maintenance_bot.send_message(user_id, "Menu item deletion cancelled.")
    show_menus_menu(message)


@maintenance_bot.message_handler(
    func=lambda message: message.text == "Delete Another Menu Item"
)
def delete_another_menu_item(message):
    """Handle delete another menu item button"""
    user_id = message.from_user.id

    if not is_authorized(user_id):
        return

    # Clear any state
    if user_id in user_states:
        user_states.pop(user_id)

    # Go to delete menu item flow
    ask_restaurant_for_menu_item_delete(message)


# Delivery Locations & Fees Management
@maintenance_bot.message_handler(
    func=lambda message: message.text == "Delivery Locations & Fees"
)
def show_delivery_menu(message):
    """Show the delivery locations and fees management menu"""
    user_id = message.from_user.id

    if not is_authorized(user_id):
        return

    markup = types.ReplyKeyboardMarkup(row_width=2, resize_keyboard=True)
    markup.add(
        types.KeyboardButton("List Delivery Locations"),
        types.KeyboardButton("Add Delivery Location"),
        types.KeyboardButton("Update Delivery Location"),
        types.KeyboardButton("Delete Delivery Location"),
        types.KeyboardButton("Back to Main Menu"),
    )

    maintenance_bot.send_message(
        user_id,
        "Delivery Locations Management. What would you like to do?",
        reply_markup=markup,
    )


@maintenance_bot.message_handler(
    func=lambda message: message.text == "View All Delivery Locations"
)
def view_all_delivery_locations(message):
    """View all delivery locations with options to manage them"""
    user_id = message.from_user.id

    if not is_authorized(user_id):
        return

    locations = get_all_delivery_locations()

    if not locations:
        # Create a keyboard with navigation options
        markup = types.ReplyKeyboardMarkup(row_width=2, resize_keyboard=True)
        markup.add(
            types.KeyboardButton("Add Delivery Location"),
            types.KeyboardButton("Back to Delivery Menu"),
            types.KeyboardButton("Back to Main Menu"),
        )

        maintenance_bot.send_message(
            user_id, "No delivery locations found.", reply_markup=markup
        )
        return

    # Generate locations text
    locations_text = "Delivery Locations:\n\n"
    for location in locations:
        locations_text += f"ID: {location['id']} - {location['name']}\n"

    locations_text += "\nSelect an action or enter a location ID to manage its fees:"

    # Create a keyboard with navigation options
    markup = types.ReplyKeyboardMarkup(row_width=2, resize_keyboard=True)
    markup.add(
        types.KeyboardButton("Add Delivery Location"),
        types.KeyboardButton("Back to Delivery Menu"),
        types.KeyboardButton("Back to Main Menu"),
    )

    msg = maintenance_bot.send_message(user_id, locations_text, reply_markup=markup)

    # Register next step handler to capture location ID input
    user_states[user_id] = {"state": "selecting_location_for_management"}
    maintenance_bot.register_next_step_handler(msg, process_location_selection)


def process_location_selection(message):
    """Process location selection for management"""
    user_id = message.from_user.id

    if not is_authorized(user_id):
        return

    # Check if the user selected a menu option instead of entering a location ID
    if message.text == "Add Delivery Location":
        ask_delivery_location_name(message)
        return
    elif message.text == "Back to Delivery Menu":
        back_to_delivery_menu(message)
        return
    elif message.text == "Back to Main Menu":
        back_to_main_menu(message)
        return

    # Try to parse the location ID
    try:
        location_id = int(message.text.strip())
    except ValueError:
        maintenance_bot.send_message(
            user_id, "Please enter a valid location ID (number)."
        )
        list_delivery_locations(message)
        return

    # Get the location
    location = get_delivery_location_by_id(location_id)
    if not location:
        maintenance_bot.send_message(
            user_id, f"Delivery location with ID {location_id} not found."
        )
        list_delivery_locations(message)
        return

    # Show location management options
    show_location_management(message, location_id)


def show_location_management(message, location_id):
    """Show management options for a specific delivery location"""
    user_id = message.from_user.id

    if not is_authorized(user_id):
        return

    # Get the location
    location = get_delivery_location_by_id(location_id)
    if not location:
        maintenance_bot.send_message(
            user_id, f"Delivery location with ID {location_id} not found."
        )
        list_delivery_locations(message)
        return

    # Get all areas (needed for UI display)
    _ = get_all_areas()

    # Get all delivery fees for this location
    all_fees = get_all_delivery_fees()
    location_fees = []
    for fee in all_fees:
        if fee["location_id"] == location_id:
            location_fees.append(fee)

    # Generate location info text
    location_text = f"Location: {location['name']} (ID: {location['id']})\n\n"

    if location_fees:
        location_text += "Delivery Fees:\n"
        for fee in location_fees:
            area = get_area_by_id(fee["area_id"])
            area_name = area["name"] if area else f"Unknown Area (ID: {fee['area_id']})"
            location_text += f"From: {area_name} - Fee: {fee['fee']} ETB\n"
    else:
        location_text += "No delivery fees set for this location.\n"

    # Create a keyboard with management options
    markup = types.ReplyKeyboardMarkup(row_width=2, resize_keyboard=True)
    markup.add(
        types.KeyboardButton(f"Edit Location {location_id}"),
        types.KeyboardButton(f"Add Fee for Location {location_id}"),
        types.KeyboardButton(f"Delete Location {location_id}"),
        types.KeyboardButton("Back to Locations List"),
        types.KeyboardButton("Back to Delivery Menu"),
        types.KeyboardButton("Back to Main Menu"),
    )

    maintenance_bot.send_message(user_id, location_text, reply_markup=markup)

    # Store the location ID in user state
    user_states[user_id] = {"state": "managing_location", "location_id": location_id}


@maintenance_bot.message_handler(
    func=lambda message: message.text and message.text.startswith("Edit Location ")
)
def edit_location_name(message):
    """Edit a delivery location name"""
    user_id = message.from_user.id

    if not is_authorized(user_id):
        return

    # Extract location ID from the message
    try:
        location_id = int(message.text.replace("Edit Location ", "").strip())
    except ValueError:
        maintenance_bot.send_message(user_id, "Invalid location ID.")
        list_delivery_locations(message)
        return

    # Get the location
    location = get_delivery_location_by_id(location_id)
    if not location:
        maintenance_bot.send_message(
            user_id, f"Delivery location with ID {location_id} not found."
        )
        list_delivery_locations(message)
        return

    # Ask for the new name
    markup = types.ForceReply(selective=True)
    msg = maintenance_bot.send_message(
        user_id,
        f"Current name: {location['name']}\nEnter the new name for the delivery location:",
        reply_markup=markup,
    )

    user_states[user_id] = {
        "state": "editing_location_name",
        "location_id": location_id,
    }
    maintenance_bot.register_next_step_handler(msg, process_edit_location_name)


def process_edit_location_name(message):
    """Process editing a delivery location name"""
    user_id = message.from_user.id

    if not is_authorized(user_id):
        return

    new_name = message.text.strip()
    if not new_name:
        maintenance_bot.send_message(user_id, "Location name cannot be empty.")
        return

    location_id = user_states[user_id].get("location_id")
    if not location_id:
        maintenance_bot.send_message(user_id, "Location ID not found.")
        list_delivery_locations(message)
        return

    # Update the location name
    updated_location = update_delivery_location(location_id, new_name)
    if updated_location:
        maintenance_bot.send_message(
            user_id,
            f"Location name updated successfully to: {updated_location['name']}",
        )
        # Show the updated location management view
        show_location_management(message, location_id)
    else:
        maintenance_bot.send_message(user_id, "Failed to update location name.")
        list_delivery_locations(message)


@maintenance_bot.message_handler(
    func=lambda message: message.text
    and message.text.startswith("Add Fee for Location ")
)
def add_fee_for_location(message):
    """Add a delivery fee for a specific location"""
    user_id = message.from_user.id

    if not is_authorized(user_id):
        return

    # Extract location ID from the message
    try:
        location_id = int(message.text.replace("Add Fee for Location ", "").strip())
    except ValueError:
        maintenance_bot.send_message(user_id, "Invalid location ID.")
        list_delivery_locations(message)
        return

    # Get the location
    location = get_delivery_location_by_id(location_id)
    if not location:
        maintenance_bot.send_message(
            user_id, f"Delivery location with ID {location_id} not found."
        )
        list_delivery_locations(message)
        return

    # Get all areas
    areas = get_all_areas()
    if not areas:
        maintenance_bot.send_message(
            user_id, "No areas found. Please add an area first."
        )
        return

    # Show areas to select from
    areas_text = f"Adding fee for location: {location['name']}\n\nAreas:\n\n"
    for area in areas:
        areas_text += f"ID: {area['id']} - {area['name']}\n"

    areas_text += "\nEnter the ID of the area for the delivery fee:"

    markup = types.ForceReply(selective=True)
    msg = maintenance_bot.send_message(user_id, areas_text, reply_markup=markup)

    user_states[user_id] = {"state": "adding_fee_area", "location_id": location_id}
    maintenance_bot.register_next_step_handler(msg, process_fee_area_selection)


def process_fee_area_selection(message):
    """Process area selection for adding a delivery fee"""
    user_id = message.from_user.id

    if not is_authorized(user_id):
        return

    try:
        area_id = int(message.text.strip())
    except ValueError:
        maintenance_bot.send_message(user_id, "Please enter a valid area ID (number).")
        return

    # Get the area
    area = get_area_by_id(area_id)
    if not area:
        maintenance_bot.send_message(user_id, f"Area with ID {area_id} not found.")
        return

    location_id = user_states[user_id].get("location_id")
    if not location_id:
        maintenance_bot.send_message(user_id, "Location ID not found.")
        list_delivery_locations(message)
        return

    # Get the location
    location = get_delivery_location_by_id(location_id)
    if not location:
        maintenance_bot.send_message(
            user_id, f"Delivery location with ID {location_id} not found."
        )
        list_delivery_locations(message)
        return

    # Ask for the fee amount
    markup = types.ForceReply(selective=True)
    msg = maintenance_bot.send_message(
        user_id,
        f"Enter the delivery fee amount (in ETB) from {area['name']} to {location['name']}:",
        reply_markup=markup,
    )

    user_states[user_id] = {
        "state": "adding_fee_amount",
        "location_id": location_id,
        "area_id": area_id,
    }
    maintenance_bot.register_next_step_handler(msg, process_fee_amount)


def process_fee_amount(message):
    """Process fee amount for adding a delivery fee"""
    user_id = message.from_user.id

    if not is_authorized(user_id):
        return

    try:
        fee_amount = float(message.text.strip())
    except ValueError:
        maintenance_bot.send_message(
            user_id, "Please enter a valid fee amount (number)."
        )
        return

    location_id = user_states[user_id].get("location_id")
    area_id = user_states[user_id].get("area_id")

    if not location_id or not area_id:
        maintenance_bot.send_message(user_id, "Location ID or area ID not found.")
        list_delivery_locations(message)
        return

    # Add the delivery fee
    new_fee = add_delivery_fee(area_id, location_id, fee_amount)

    if new_fee:
        maintenance_bot.send_message(
            user_id,
            f"Delivery fee added successfully! Fee: {new_fee['fee']} ETB",
        )
        # Show the updated location management view
        show_location_management(message, location_id)
    else:
        maintenance_bot.send_message(user_id, "Failed to add delivery fee.")
        list_delivery_locations(message)


@maintenance_bot.message_handler(
    func=lambda message: message.text and message.text.startswith("Delete Location ")
)
def delete_location_with_fees(message):
    """Delete a delivery location and optionally its associated fees"""
    user_id = message.from_user.id

    if not is_authorized(user_id):
        return

    # Extract location ID from the message
    try:
        location_id = int(message.text.replace("Delete Location ", "").strip())
    except ValueError:
        maintenance_bot.send_message(user_id, "Invalid location ID.")
        list_delivery_locations(message)
        return

    # Get the location
    location = get_delivery_location_by_id(location_id)
    if not location:
        maintenance_bot.send_message(
            user_id, f"Delivery location with ID {location_id} not found."
        )
        list_delivery_locations(message)
        return

    # Check if there are any fees associated with this location
    all_fees = get_all_delivery_fees()
    location_fees = []
    for fee in all_fees:
        if fee["location_id"] == location_id:
            location_fees.append(fee)

    # Create confirmation message
    confirmation_text = (
        f"Are you sure you want to delete the location '{location['name']}'?"
    )

    if location_fees:
        confirmation_text += f"\n\nThis location has {len(location_fees)} delivery fees that will also be deleted."

    confirmation_text += "\n\nThis action cannot be undone."

    # Create confirmation keyboard
    markup = types.ReplyKeyboardMarkup(row_width=2, resize_keyboard=True)
    markup.add(
        types.KeyboardButton(f"Confirm Delete Location {location_id}"),
        types.KeyboardButton("Cancel Delete"),
        types.KeyboardButton("Back to Locations List"),
        types.KeyboardButton("Back to Delivery Menu"),
    )

    maintenance_bot.send_message(user_id, confirmation_text, reply_markup=markup)

    user_states[user_id] = {
        "state": "confirming_location_delete",
        "location_id": location_id,
        "has_fees": bool(location_fees),
    }


@maintenance_bot.message_handler(
    func=lambda message: message.text == "Back to Locations List"
)
def back_to_locations_list(message):
    """Go back to the delivery locations list"""
    user_id = message.from_user.id

    if not is_authorized(user_id):
        return

    # Clear any state
    if user_id in user_states:
        user_states.pop(user_id)

    # Go back to locations list
    list_delivery_locations(message)


@maintenance_bot.message_handler(
    func=lambda message: message.text == "View Delivery Fees by Area"
)
def view_delivery_fees_by_area(message):
    """View delivery fees organized by area"""
    user_id = message.from_user.id

    if not is_authorized(user_id):
        return

    # Get all areas
    areas = get_all_areas()
    if not areas:
        maintenance_bot.send_message(
            user_id, "No areas found. Please add an area first."
        )
        return

    # Get all delivery locations
    locations = get_all_delivery_locations()
    if not locations:
        maintenance_bot.send_message(
            user_id,
            "No delivery locations found. Please add a delivery location first.",
        )
        return

    # Get all delivery fees
    fees = get_all_delivery_fees()
    if not fees:
        maintenance_bot.send_message(user_id, "No delivery fees found.")
        show_delivery_menu(message)
        return

    # Organize fees by area
    fees_by_area = {}
    for fee in fees:
        area_id = fee["area_id"]
        if area_id not in fees_by_area:
            fees_by_area[area_id] = []
        fees_by_area[area_id].append(fee)

    # Generate fees text
    fees_text = "Delivery Fees by Area:\n\n"
    for area_id, area_fees in fees_by_area.items():
        area = get_area_by_id(area_id)
        area_name = area["name"] if area else f"Unknown Area (ID: {area_id})"
        fees_text += f"Area: {area_name}\n"

        for fee in area_fees:
            location = get_delivery_location_by_id(fee["location_id"])
            location_name = (
                location["name"]
                if location
                else f"Unknown Location (ID: {fee['location_id']})"
            )
            fees_text += f"  To: {location_name} - Fee: {fee['fee']} ETB\n"

        fees_text += "\n"

    # Create a keyboard with navigation options
    markup = types.ReplyKeyboardMarkup(row_width=2, resize_keyboard=True)
    markup.add(
        types.KeyboardButton("View All Delivery Locations"),
        types.KeyboardButton("Add Delivery Location"),
        types.KeyboardButton("Back to Delivery Menu"),
        types.KeyboardButton("Back to Main Menu"),
    )

    maintenance_bot.send_message(user_id, fees_text, reply_markup=markup)


@maintenance_bot.message_handler(
    func=lambda message: message.text == "List Delivery Fees"
)
def list_delivery_fees(message):
    """List all delivery fees"""
    user_id = message.from_user.id

    if not is_authorized(user_id):
        return

    fees = get_all_delivery_fees()

    if not fees:
        # Create a keyboard with navigation options
        markup = types.ReplyKeyboardMarkup(row_width=2, resize_keyboard=True)
        markup.add(
            types.KeyboardButton("Add Delivery Fee"),
            types.KeyboardButton("Back to Delivery Menu"),
            types.KeyboardButton("Back to Main Menu"),
        )

        maintenance_bot.send_message(
            user_id, "No delivery fees found.", reply_markup=markup
        )
        return

    # Generate fees text
    fees_text = "Delivery Fees:\n\n"
    for i, fee in enumerate(fees):
        # Get area and location names
        area = get_area_by_id(fee["area_id"])
        location = get_delivery_location_by_id(fee["location_id"])

        area_name = area["name"] if area else f"Unknown Area (ID: {fee['area_id']})"
        location_name = (
            location["name"]
            if location
            else f"Unknown Location (ID: {fee['location_id']})"
        )

        fees_text += f"Index: {i+1} - From: {area_name} - To: {location_name} - Fee: {fee['fee']} ETB\n"

    # Create a keyboard with navigation options
    markup = types.ReplyKeyboardMarkup(row_width=2, resize_keyboard=True)
    markup.add(
        types.KeyboardButton("Add Delivery Fee"),
        types.KeyboardButton("Update Delivery Fee"),
        types.KeyboardButton("Delete Delivery Fee"),
        types.KeyboardButton("Back to Delivery Menu"),
        types.KeyboardButton("Back to Main Menu"),
    )

    maintenance_bot.send_message(user_id, fees_text, reply_markup=markup)


@maintenance_bot.message_handler(
    func=lambda message: message.text == "Back to Delivery Menu"
)
def back_to_delivery_menu(message):
    """Handle back to delivery menu button"""
    user_id = message.from_user.id

    if not is_authorized(user_id):
        return

    # Clear any state
    if user_id in user_states:
        user_states.pop(user_id)

    # Go back to delivery menu
    show_delivery_menu(message)


@maintenance_bot.message_handler(
    func=lambda message: message.text == "Add Delivery Location"
)
def ask_delivery_location_name(message):
    """Ask for delivery location name to add"""
    user_id = message.from_user.id

    if not is_authorized(user_id):
        return

    markup = types.ForceReply(selective=True)
    msg = maintenance_bot.send_message(
        user_id, "Enter the name of the new delivery location:", reply_markup=markup
    )

    user_states[user_id] = {"state": "adding_delivery_location_name"}
    maintenance_bot.register_next_step_handler(msg, process_add_delivery_location_name)


def process_add_delivery_location_name(message):
    """Process adding a new delivery location name"""
    user_id = message.from_user.id

    if not is_authorized(user_id):
        return

    location_name = message.text.strip()

    if not location_name:
        maintenance_bot.send_message(user_id, "Delivery location name cannot be empty.")
        return

    # Add the location to the database
    new_location = add_delivery_location(location_name)

    if not new_location:
        maintenance_bot.send_message(user_id, "Failed to add delivery location.")
        return

    # Get all areas to set fees for each area
    areas = get_all_areas()
    if not areas:
        maintenance_bot.send_message(user_id, "No areas found. Please add areas first.")
        return

    # Store the new location in user state
    user_states[user_id] = {
        "state": "adding_delivery_location_fees",
        "location_id": new_location["id"],
        "location_name": new_location["name"],
        "remaining_areas": [area["id"] for area in areas],
        "current_area_index": 0,
    }

    # Start adding fees for each area
    process_next_area_fee(message)


def process_next_area_fee(message):
    """Process adding fee for the next area"""
    user_id = message.from_user.id

    if not is_authorized(user_id):
        return

    # Get user state
    state = user_states.get(user_id, {})
    if state.get("state") != "adding_delivery_location_fees":
        maintenance_bot.send_message(user_id, "Invalid state.")
        return

    # Get remaining areas
    remaining_areas = state.get("remaining_areas", [])
    if not remaining_areas:
        # All areas processed, show completion message
        markup = types.ReplyKeyboardMarkup(row_width=2, resize_keyboard=True)
        markup.add(
            types.KeyboardButton("Add Another Delivery Location"),
            types.KeyboardButton("List Delivery Locations"),
            types.KeyboardButton("Back to Delivery Menu"),
            types.KeyboardButton("Back to Main Menu"),
        )

        maintenance_bot.send_message(
            user_id,
            f"Delivery location '{state.get('location_name')}' added successfully with fees for all areas!",
            reply_markup=markup,
        )
        return

    # Get the next area
    area_id = remaining_areas[0]
    area = get_area_by_id(area_id)

    if not area:
        # Skip this area if not found
        state["remaining_areas"] = remaining_areas[1:]
        process_next_area_fee(message)
        return

    # Ask for fee for this area
    markup = types.ForceReply(selective=True)
    msg = maintenance_bot.send_message(
        user_id,
        f"Enter the delivery fee (in ETB) from {area['name']} to {state.get('location_name')}:\n(Enter 0 if no fee or to skip)",
        reply_markup=markup,
    )

    # Update state to current area
    state["current_area_id"] = area_id
    user_states[user_id] = state

    maintenance_bot.register_next_step_handler(msg, process_area_fee_amount)


def process_area_fee_amount(message):
    """Process fee amount for an area"""
    user_id = message.from_user.id

    if not is_authorized(user_id):
        return

    # Get user state
    state = user_states.get(user_id, {})
    if state.get("state") != "adding_delivery_location_fees":
        maintenance_bot.send_message(user_id, "Invalid state.")
        return

    # Get current area and location
    area_id = state.get("current_area_id")
    location_id = state.get("location_id")

    if not area_id or not location_id:
        maintenance_bot.send_message(user_id, "Area ID or location ID not found.")
        return

    # Parse fee amount
    try:
        # Convert fee to int instead of float
        fee_amount = int(float(message.text.strip()))
    except ValueError:
        maintenance_bot.send_message(
            user_id, "Please enter a valid fee amount (number)."
        )
        # Ask again for this area
        area = get_area_by_id(area_id)
        markup = types.ForceReply(selective=True)
        msg = maintenance_bot.send_message(
            user_id,
            f"Enter the delivery fee (in ETB) from {area['name']} to {state.get('location_name')}:\n(Enter 0 if no fee or to skip)",
            reply_markup=markup,
        )
        maintenance_bot.register_next_step_handler(msg, process_area_fee_amount)
        return

    # Add fee if not zero
    if fee_amount > 0:
        add_delivery_fee(area_id, location_id, fee_amount)

    # Move to next area
    remaining_areas = state.get("remaining_areas", [])
    state["remaining_areas"] = remaining_areas[1:]
    user_states[user_id] = state

    # Process next area
    process_next_area_fee(message)


@maintenance_bot.message_handler(
    func=lambda message: message.text == "Add Another Delivery Location"
)
def add_another_delivery_location(message):
    """Handle add another delivery location button"""
    user_id = message.from_user.id

    if not is_authorized(user_id):
        return

    # Clear any state
    if user_id in user_states:
        user_states.pop(user_id)

    # Go to add delivery location flow
    ask_delivery_location_name(message)


# Old update delivery location handler removed


# Old update_another_delivery_location handler removed


# Backward compatibility handlers
@maintenance_bot.message_handler(
    func=lambda message: message.text == "List Delivery Locations"
)
def list_delivery_locations(message):
    """List all delivery locations grouped by areas with prices"""
    user_id = message.from_user.id

    if not is_authorized(user_id):
        return

    # Get all areas
    areas = get_all_areas()
    if not areas:
        maintenance_bot.send_message(
            user_id, "No areas found. Please add an area first."
        )
        return

    # Get all delivery locations
    locations = get_all_delivery_locations()
    if not locations:
        # Create a keyboard with navigation options
        markup = types.ReplyKeyboardMarkup(row_width=2, resize_keyboard=True)
        markup.add(
            types.KeyboardButton("Add Delivery Location"),
            types.KeyboardButton("Back to Delivery Menu"),
            types.KeyboardButton("Back to Main Menu"),
        )

        maintenance_bot.send_message(
            user_id, "No delivery locations found.", reply_markup=markup
        )
        return

    # Get all delivery fees
    fees = get_all_delivery_fees()

    # Organize locations and fees by area
    locations_by_area = {}
    for area in areas:
        locations_by_area[area["id"]] = {"area_name": area["name"], "locations": []}

    # Find fees for each location in each area
    for location in locations:
        for area in areas:
            # Find fee for this area-location pair
            fee_amount = 0
            for fee in fees:
                if (
                    fee["area_id"] == area["id"]
                    and fee["location_id"] == location["id"]
                ):
                    fee_amount = fee["fee"]
                    break

            # Add location with fee to this area
            locations_by_area[area["id"]]["locations"].append(
                {"id": location["id"], "name": location["name"], "fee": fee_amount}
            )

    # Generate locations text grouped by area
    locations_text = "Delivery Locations by Area:\n\n"
    for _, area_data in locations_by_area.items():
        locations_text += f"Area: {area_data['area_name']}\n"
        for loc in area_data["locations"]:
            fee_text = f"{loc['fee']} ETB" if loc["fee"] > 0 else "Not set"
            locations_text += f"  ID: {loc['id']} - {loc['name']} - Fee: {fee_text}\n"
        locations_text += "\n"

    # Create a keyboard with navigation options
    markup = types.ReplyKeyboardMarkup(row_width=2, resize_keyboard=True)
    markup.add(
        types.KeyboardButton("Add Delivery Location"),
        types.KeyboardButton("Update Delivery Location"),
        types.KeyboardButton("Delete Delivery Location"),
        types.KeyboardButton("Back to Delivery Menu"),
        types.KeyboardButton("Back to Main Menu"),
    )

    maintenance_bot.send_message(user_id, locations_text, reply_markup=markup)


@maintenance_bot.message_handler(
    func=lambda message: message.text == "Delete Delivery Location"
)
def delete_delivery_location_handler(message):
    """Delete a delivery location - first select location"""
    user_id = message.from_user.id

    if not is_authorized(user_id):
        return

    # Get all delivery locations
    locations = get_all_delivery_locations()
    if not locations:
        maintenance_bot.send_message(user_id, "No delivery locations found to delete.")
        return

    # Show locations to select from
    locations_text = "Delivery Locations:\n\n"
    for location in locations:
        locations_text += f"ID: {location['id']} - {location['name']}\n"

    locations_text += "\nEnter the ID of the location you want to delete:"

    markup = types.ForceReply(selective=True)
    msg = maintenance_bot.send_message(user_id, locations_text, reply_markup=markup)

    user_states[user_id] = {"state": "selecting_location_to_delete"}
    maintenance_bot.register_next_step_handler(
        msg, process_location_selection_for_delete
    )


def process_location_selection_for_delete(message):
    """Process location selection for delete"""
    user_id = message.from_user.id

    if not is_authorized(user_id):
        return

    try:
        location_id = int(message.text.strip())
    except ValueError:
        maintenance_bot.send_message(
            user_id, "Please enter a valid location ID (number)."
        )
        delete_delivery_location_handler(message)
        return

    # Get the location
    location = get_delivery_location_by_id(location_id)
    if not location:
        maintenance_bot.send_message(
            user_id, f"Location with ID {location_id} not found."
        )
        delete_delivery_location_handler(message)
        return

    # Check if there are any fees associated with this location
    fees = get_all_delivery_fees()
    location_fees = [fee for fee in fees if fee["location_id"] == location_id]

    # Show delete confirmation options
    markup = types.ReplyKeyboardMarkup(row_width=2, resize_keyboard=True)
    markup.add(
        types.KeyboardButton(f"Confirm Delete Location {location_id}"),
        types.KeyboardButton("Cancel Delete"),
        types.KeyboardButton("Back to Delivery Menu"),
        types.KeyboardButton("Back to Main Menu"),
    )

    confirmation_text = f"Are you sure you want to delete this delivery location?\n\nID: {location['id']}\nName: {location['name']}"

    if location_fees:
        confirmation_text += (
            f"\n\nThis will also delete {len(location_fees)} associated delivery fees."
        )

    confirmation_text += "\n\nThis action cannot be undone."

    maintenance_bot.send_message(user_id, confirmation_text, reply_markup=markup)

    user_states[user_id] = {
        "state": "confirming_location_delete",
        "location_id": location_id,
    }


@maintenance_bot.message_handler(
    func=lambda message: message.text
    and message.text.startswith("Confirm Delete Location ")
    and user_states.get(message.from_user.id, {}).get("state")
    == "confirming_location_delete"
)
def confirm_delete_location(message):
    """Confirm and process location deletion"""
    user_id = message.from_user.id

    if not is_authorized(user_id):
        return

    location_id = user_states[user_id].get("location_id")
    if not location_id:
        maintenance_bot.send_message(user_id, "Location ID not found.")
        delete_delivery_location_handler(message)
        return

    # Get the location
    location = get_delivery_location_by_id(location_id)
    if not location:
        maintenance_bot.send_message(
            user_id, f"Location with ID {location_id} not found."
        )
        delete_delivery_location_handler(message)
        return

    # Get all fees associated with this location
    fees = get_all_delivery_fees()
    location_fees = [fee for fee in fees if fee["location_id"] == location_id]
    fee_count = len(location_fees)

    # Delete all associated fees first
    for fee in location_fees:
        delete_delivery_fee(fee["area_id"], fee["location_id"])

    # Delete the location
    success = delete_delivery_location(location_id)

    if success:
        # Create a keyboard with navigation options
        markup = types.ReplyKeyboardMarkup(row_width=2, resize_keyboard=True)
        markup.add(
            types.KeyboardButton("Delete Another Delivery Location"),
            types.KeyboardButton("List Delivery Locations"),
            types.KeyboardButton("Back to Delivery Menu"),
            types.KeyboardButton("Back to Main Menu"),
        )

        result_text = f"Delivery location '{location['name']}' deleted successfully!"
        if fee_count > 0:
            result_text += f"\n{fee_count} associated delivery fees were also deleted."

        maintenance_bot.send_message(user_id, result_text, reply_markup=markup)
    else:
        maintenance_bot.send_message(user_id, "Failed to delete delivery location.")
        delete_delivery_location_handler(message)


@maintenance_bot.message_handler(
    func=lambda message: message.text == "Cancel Delete"
    and user_states.get(message.from_user.id, {}).get("state")
    == "confirming_location_delete"
)
def cancel_delete_location(message):
    """Cancel location deletion"""
    user_id = message.from_user.id

    if not is_authorized(user_id):
        return

    # Clear the state
    if user_id in user_states:
        user_states.pop(user_id)

    maintenance_bot.send_message(user_id, "Delivery location deletion cancelled.")
    list_delivery_locations(message)


@maintenance_bot.message_handler(
    func=lambda message: message.text == "Delete Another Delivery Location"
)
def delete_another_delivery_location(message):
    """Handle delete another delivery location button"""
    user_id = message.from_user.id

    if not is_authorized(user_id):
        return

    # Clear any state
    if user_id in user_states:
        user_states.pop(user_id)

    # Go to delete delivery location flow
    delete_delivery_location_handler(message)


@maintenance_bot.message_handler(
    func=lambda message: message.text == "Update Delivery Location"
)
def update_delivery_location_handler(message):
    """Update a delivery location - first select area"""
    user_id = message.from_user.id

    if not is_authorized(user_id):
        return

    # Get all areas
    areas = get_all_areas()
    if not areas:
        maintenance_bot.send_message(
            user_id, "No areas found. Please add an area first."
        )
        return

    # Show areas to select from
    areas_text = "Select an area to view its delivery locations:\n\n"
    for area in areas:
        areas_text += f"ID: {area['id']} - {area['name']}\n"

    markup = types.ForceReply(selective=True)
    msg = maintenance_bot.send_message(user_id, areas_text, reply_markup=markup)

    user_states[user_id] = {"state": "selecting_area_for_location_update"}
    maintenance_bot.register_next_step_handler(msg, process_area_selection_for_update)


def process_area_selection_for_update(message):
    """Process area selection for location update"""
    user_id = message.from_user.id

    if not is_authorized(user_id):
        return

    try:
        area_id = int(message.text.strip())
    except ValueError:
        maintenance_bot.send_message(user_id, "Please enter a valid area ID (number).")
        update_delivery_location_handler(message)
        return

    # Get the area
    area = get_area_by_id(area_id)
    if not area:
        maintenance_bot.send_message(user_id, f"Area with ID {area_id} not found.")
        update_delivery_location_handler(message)
        return

    # Get all delivery locations
    locations = get_all_delivery_locations()
    if not locations:
        maintenance_bot.send_message(user_id, "No delivery locations found.")
        return

    # Get all delivery fees for this area
    fees = get_all_delivery_fees()
    area_fees = [fee for fee in fees if fee["area_id"] == area_id]

    # Show locations with fees for this area
    locations_text = f"Delivery Locations for {area['name']}:\n\n"
    for location in locations:
        # Find fee for this location in this area
        fee_amount = 0
        for fee in area_fees:
            if fee["location_id"] == location["id"]:
                fee_amount = fee["fee"]
                break

        fee_text = f"{fee_amount} ETB" if fee_amount > 0 else "Not set"
        locations_text += (
            f"ID: {location['id']} - {location['name']} - Fee: {fee_text}\n"
        )

    locations_text += "\nEnter the ID of the location you want to update:"

    markup = types.ForceReply(selective=True)
    msg = maintenance_bot.send_message(user_id, locations_text, reply_markup=markup)

    user_states[user_id] = {"state": "selecting_location_to_update", "area_id": area_id}
    maintenance_bot.register_next_step_handler(
        msg, process_location_selection_for_update
    )


def process_location_selection_for_update(message):
    """Process location selection for update"""
    user_id = message.from_user.id

    if not is_authorized(user_id):
        return

    try:
        location_id = int(message.text.strip())
    except ValueError:
        maintenance_bot.send_message(
            user_id, "Please enter a valid location ID (number)."
        )
        update_delivery_location_handler(message)
        return

    # Get the location
    location = get_delivery_location_by_id(location_id)
    if not location:
        maintenance_bot.send_message(
            user_id, f"Location with ID {location_id} not found."
        )
        update_delivery_location_handler(message)
        return

    # Get the area ID from state
    area_id = user_states[user_id].get("area_id")
    if not area_id:
        maintenance_bot.send_message(user_id, "Area ID not found.")
        update_delivery_location_handler(message)
        return

    # Get the area
    area = get_area_by_id(area_id)
    if not area:
        maintenance_bot.send_message(user_id, f"Area with ID {area_id} not found.")
        update_delivery_location_handler(message)
        return

    # Get the current fee
    fees = get_all_delivery_fees()
    fee_amount = 0
    for fee in fees:
        if fee["area_id"] == area_id and fee["location_id"] == location_id:
            fee_amount = fee["fee"]
            break

    # Show update options
    markup = types.ReplyKeyboardMarkup(row_width=2, resize_keyboard=True)
    markup.add(
        types.KeyboardButton(f"Update Name for Location {location_id}"),
        types.KeyboardButton(
            f"Update Fee for Location {location_id} in {area['name']}"
        ),
        types.KeyboardButton("Back to Delivery Menu"),
        types.KeyboardButton("Back to Main Menu"),
    )

    fee_text = f"{fee_amount} ETB" if fee_amount > 0 else "Not set"
    maintenance_bot.send_message(
        user_id,
        f"Location: {location['name']} (ID: {location_id})\nArea: {area['name']}\nCurrent Fee: {fee_text}\n\nWhat would you like to update?",
        reply_markup=markup,
    )

    # Store location and area IDs in state
    user_states[user_id] = {
        "state": "updating_location",
        "location_id": location_id,
        "area_id": area_id,
    }


@maintenance_bot.message_handler(
    func=lambda message: message.text
    and message.text.startswith("Update Name for Location ")
)
def ask_update_location_name(message):
    """Ask for new name for location"""
    user_id = message.from_user.id

    if not is_authorized(user_id):
        return

    # Extract location ID from message
    try:
        location_id = int(message.text.replace("Update Name for Location ", "").strip())
    except ValueError:
        maintenance_bot.send_message(user_id, "Invalid location ID.")
        update_delivery_location_handler(message)
        return

    # Get the location
    location = get_delivery_location_by_id(location_id)
    if not location:
        maintenance_bot.send_message(
            user_id, f"Location with ID {location_id} not found."
        )
        update_delivery_location_handler(message)
        return

    # Ask for new name
    markup = types.ForceReply(selective=True)
    msg = maintenance_bot.send_message(
        user_id,
        f"Current name: {location['name']}\nEnter the new name for the delivery location:",
        reply_markup=markup,
    )

    user_states[user_id] = {
        "state": "updating_location_name",
        "location_id": location_id,
    }
    maintenance_bot.register_next_step_handler(msg, process_update_location_name)


def process_update_location_name(message):
    """Process updating location name"""
    user_id = message.from_user.id

    if not is_authorized(user_id):
        return

    new_name = message.text.strip()
    if not new_name:
        maintenance_bot.send_message(user_id, "Location name cannot be empty.")
        return

    location_id = user_states[user_id].get("location_id")
    if not location_id:
        maintenance_bot.send_message(user_id, "Location ID not found.")
        update_delivery_location_handler(message)
        return

    # Update the location name
    updated_location = update_delivery_location(location_id, new_name)

    if updated_location:
        # Create a keyboard with navigation options
        markup = types.ReplyKeyboardMarkup(row_width=2, resize_keyboard=True)
        markup.add(
            types.KeyboardButton("Update Another Delivery Location"),
            types.KeyboardButton("List Delivery Locations"),
            types.KeyboardButton("Back to Delivery Menu"),
            types.KeyboardButton("Back to Main Menu"),
        )

        maintenance_bot.send_message(
            user_id,
            f"Location name updated successfully to: {updated_location['name']}",
            reply_markup=markup,
        )
    else:
        maintenance_bot.send_message(user_id, "Failed to update location name.")
        update_delivery_location_handler(message)


@maintenance_bot.message_handler(
    func=lambda message: message.text and "Update Fee for Location " in message.text
)
def ask_update_location_fee(message):
    """Ask for new fee for location in area"""
    user_id = message.from_user.id

    if not is_authorized(user_id):
        return

    # Extract location ID from message
    try:
        # Extract the location ID from the message text
        location_id_str = message.text.split("Update Fee for Location ")[1].split(
            " in "
        )[0]
        location_id = int(location_id_str.strip())
    except (ValueError, IndexError):
        maintenance_bot.send_message(user_id, "Invalid location ID.")
        update_delivery_location_handler(message)
        return

    # Get the location
    location = get_delivery_location_by_id(location_id)
    if not location:
        maintenance_bot.send_message(
            user_id, f"Location with ID {location_id} not found."
        )
        update_delivery_location_handler(message)
        return

    # Get the area ID from state
    area_id = user_states[user_id].get("area_id")
    if not area_id:
        # Try to extract area name from message and find area ID
        try:
            area_name = message.text.split(" in ")[1].strip()
            areas = get_all_areas()
            area_id = None
            for area in areas:
                if area["name"] == area_name:
                    area_id = area["id"]
                    break

            if not area_id:
                maintenance_bot.send_message(user_id, f"Area '{area_name}' not found.")
                update_delivery_location_handler(message)
                return
        except (IndexError, KeyError):
            maintenance_bot.send_message(user_id, "Area information not found.")
            update_delivery_location_handler(message)
            return

    # Get the area
    area = get_area_by_id(area_id)
    if not area:
        maintenance_bot.send_message(user_id, f"Area with ID {area_id} not found.")
        update_delivery_location_handler(message)
        return

    # Get current fee
    fees = get_all_delivery_fees()
    current_fee = 0
    for fee in fees:
        if fee["area_id"] == area_id and fee["location_id"] == location_id:
            current_fee = fee["fee"]
            break

    # Ask for new fee
    markup = types.ForceReply(selective=True)
    msg = maintenance_bot.send_message(
        user_id,
        f"Current fee from {area['name']} to {location['name']}: {current_fee} ETB\nEnter the new fee amount:",
        reply_markup=markup,
    )

    user_states[user_id] = {
        "state": "updating_location_fee",
        "location_id": location_id,
        "area_id": area_id,
    }
    maintenance_bot.register_next_step_handler(msg, process_update_location_fee)


def process_update_location_fee(message):
    """Process updating location fee"""
    user_id = message.from_user.id

    if not is_authorized(user_id):
        return

    try:
        new_fee = float(message.text.strip())
    except ValueError:
        maintenance_bot.send_message(
            user_id, "Please enter a valid fee amount (number)."
        )
        return

    location_id = user_states[user_id].get("location_id")
    area_id = user_states[user_id].get("area_id")

    if not location_id or not area_id:
        maintenance_bot.send_message(user_id, "Location ID or area ID not found.")
        update_delivery_location_handler(message)
        return

    # Get location and area names for better UX
    location = get_delivery_location_by_id(location_id)
    area = get_area_by_id(area_id)

    if not location or not area:
        maintenance_bot.send_message(user_id, "Location or area not found.")
        update_delivery_location_handler(message)
        return

    # Update the fee
    updated_fee = add_delivery_fee(area_id, location_id, new_fee)

    if updated_fee:
        # Create a keyboard with navigation options
        markup = types.ReplyKeyboardMarkup(row_width=2, resize_keyboard=True)
        markup.add(
            types.KeyboardButton("Update Another Delivery Location"),
            types.KeyboardButton("List Delivery Locations"),
            types.KeyboardButton("Back to Delivery Menu"),
            types.KeyboardButton("Back to Main Menu"),
        )

        maintenance_bot.send_message(
            user_id,
            f"Delivery fee updated successfully!\nFrom: {area['name']}\nTo: {location['name']}\nNew Fee: {updated_fee['fee']} ETB",
            reply_markup=markup,
        )
    else:
        maintenance_bot.send_message(user_id, "Failed to update delivery fee.")
        update_delivery_location_handler(message)


@maintenance_bot.message_handler(
    func=lambda message: message.text == "Update Another Delivery Location"
)
def update_another_delivery_location(message):
    """Handle update another delivery location button"""
    user_id = message.from_user.id

    if not is_authorized(user_id):
        return

    # Clear any state
    if user_id in user_states:
        user_states.pop(user_id)

    # Go to update delivery location flow
    update_delivery_location_handler(message)


# Delivery Fee Management Handlers
@maintenance_bot.message_handler(
    func=lambda message: message.text == "Update Delivery Fee"
)
def ask_update_delivery_fee(message):
    """Ask for delivery fee to update"""
    user_id = message.from_user.id

    if not is_authorized(user_id):
        return

    # Get all delivery fees
    fees = get_all_delivery_fees()
    if not fees:
        maintenance_bot.send_message(user_id, "No delivery fees found to update.")
        show_delivery_fees_menu(message)
        return

    # Show fees to select from
    fees_text = "Delivery Fees:\n\n"
    for i, fee in enumerate(fees):
        area = get_area_by_id(fee["area_id"])
        location = get_delivery_location_by_id(fee["location_id"])

        area_name = area["name"] if area else f"Unknown Area (ID: {fee['area_id']})"
        location_name = (
            location["name"]
            if location
            else f"Unknown Location (ID: {fee['location_id']})"
        )

        fees_text += f"Index: {i+1} - From: {area_name} - To: {location_name} - Fee: {fee['fee']} ETB\n"

    fees_text += "\nEnter the index number of the fee you want to update:"

    markup = types.ForceReply(selective=True)
    msg = maintenance_bot.send_message(user_id, fees_text, reply_markup=markup)

    # Store all fees in user state for reference
    user_states[user_id] = {"state": "selecting_fee_to_update", "fees": fees}
    maintenance_bot.register_next_step_handler(msg, process_fee_selection_for_update)


def process_fee_selection_for_update(message):
    """Process fee selection for update"""
    user_id = message.from_user.id

    if not is_authorized(user_id):
        return

    try:
        index = int(message.text.strip()) - 1  # Convert to 0-based index
    except ValueError:
        maintenance_bot.send_message(user_id, "Please enter a valid index number.")
        show_delivery_fees_menu(message)
        return

    fees = user_states[user_id].get("fees", [])
    if not fees or index < 0 or index >= len(fees):
        maintenance_bot.send_message(user_id, "Invalid fee index.")
        show_delivery_fees_menu(message)
        return

    selected_fee = fees[index]
    area_id = selected_fee["area_id"]
    location_id = selected_fee["location_id"]
    current_fee = selected_fee["fee"]

    # Get area and location names for better UX
    area = get_area_by_id(area_id)
    location = get_delivery_location_by_id(location_id)

    area_name = area["name"] if area else f"Unknown Area (ID: {area_id})"
    location_name = (
        location["name"] if location else f"Unknown Location (ID: {location_id})"
    )

    # Ask for the new fee amount
    markup = types.ForceReply(selective=True)
    msg = maintenance_bot.send_message(
        user_id,
        f"Current fee from {area_name} to {location_name} is {current_fee} ETB.\n\nEnter the new fee amount:",
        reply_markup=markup,
    )

    user_states[user_id] = {
        "state": "updating_fee_amount",
        "area_id": area_id,
        "location_id": location_id,
    }
    maintenance_bot.register_next_step_handler(msg, process_update_fee_amount)


def process_update_fee_amount(message):
    """Process updating fee amount"""
    user_id = message.from_user.id

    if not is_authorized(user_id):
        return

    try:
        # Convert fee to int instead of float
        new_fee = int(float(message.text.strip()))
    except ValueError:
        maintenance_bot.send_message(
            user_id, "Please enter a valid fee amount (number)."
        )
        show_delivery_fees_menu(message)
        return

    area_id = user_states[user_id].get("area_id")
    location_id = user_states[user_id].get("location_id")

    if not area_id or not location_id:
        maintenance_bot.send_message(user_id, "Area ID or location ID not found.")
        show_delivery_fees_menu(message)
        return

    # Update the fee (add_delivery_fee will update if it exists)
    updated_fee = add_delivery_fee(area_id, location_id, new_fee)

    if updated_fee:
        area = get_area_by_id(area_id)
        location = get_delivery_location_by_id(location_id)

        area_name = area["name"] if area else f"Unknown Area (ID: {area_id})"
        location_name = (
            location["name"] if location else f"Unknown Location (ID: {location_id})"
        )

        # Create a keyboard with navigation options
        markup = types.ReplyKeyboardMarkup(row_width=2, resize_keyboard=True)
        markup.add(
            types.KeyboardButton("Update Another Delivery Fee"),
            types.KeyboardButton("List Delivery Fees"),
            types.KeyboardButton("Back to Delivery Menu"),
            types.KeyboardButton("Back to Main Menu"),
        )

        maintenance_bot.send_message(
            user_id,
            f"Delivery fee updated successfully!\nFrom: {area_name}\nTo: {location_name}\nNew Fee: {updated_fee['fee']} ETB",
            reply_markup=markup,
        )
    else:
        maintenance_bot.send_message(user_id, "Failed to update delivery fee.")
        show_delivery_fees_menu(message)


@maintenance_bot.message_handler(
    func=lambda message: message.text == "Update Another Delivery Fee"
)
def update_another_delivery_fee(message):
    """Handle update another delivery fee button"""
    user_id = message.from_user.id

    if not is_authorized(user_id):
        return

    # Clear any state
    if user_id in user_states:
        user_states.pop(user_id)

    # Go to update delivery fee flow
    ask_update_delivery_fee(message)


@maintenance_bot.message_handler(
    func=lambda message: message.text == "Delete Delivery Fee"
)
def ask_delete_delivery_fee(message):
    """Ask for delivery fee to delete"""
    user_id = message.from_user.id

    if not is_authorized(user_id):
        return

    # Get all delivery fees
    fees = get_all_delivery_fees()
    if not fees:
        maintenance_bot.send_message(user_id, "No delivery fees found to delete.")
        show_delivery_fees_menu(message)
        return

    # Show fees to select from
    fees_text = "Delivery Fees:\n\n"
    for i, fee in enumerate(fees):
        area = get_area_by_id(fee["area_id"])
        location = get_delivery_location_by_id(fee["location_id"])

        area_name = area["name"] if area else f"Unknown Area (ID: {fee['area_id']})"
        location_name = (
            location["name"]
            if location
            else f"Unknown Location (ID: {fee['location_id']})"
        )

        fees_text += f"Index: {i+1} - From: {area_name} - To: {location_name} - Fee: {fee['fee']} ETB\n"

    fees_text += "\nEnter the index number of the fee you want to delete:"

    markup = types.ForceReply(selective=True)
    msg = maintenance_bot.send_message(user_id, fees_text, reply_markup=markup)

    # Store all fees in user state for reference
    user_states[user_id] = {"state": "selecting_fee_to_delete", "fees": fees}
    maintenance_bot.register_next_step_handler(msg, process_fee_selection_for_delete)


def process_fee_selection_for_delete(message):
    """Process fee selection for delete"""
    user_id = message.from_user.id

    if not is_authorized(user_id):
        return

    try:
        index = int(message.text.strip()) - 1  # Convert to 0-based index
    except ValueError:
        maintenance_bot.send_message(user_id, "Please enter a valid index number.")
        show_delivery_fees_menu(message)
        return

    fees = user_states[user_id].get("fees", [])
    if not fees or index < 0 or index >= len(fees):
        maintenance_bot.send_message(user_id, "Invalid fee index.")
        show_delivery_fees_menu(message)
        return

    selected_fee = fees[index]
    area_id = selected_fee["area_id"]
    location_id = selected_fee["location_id"]
    current_fee = selected_fee["fee"]

    # Get area and location names for better UX
    area = get_area_by_id(area_id)
    location = get_delivery_location_by_id(location_id)

    area_name = area["name"] if area else f"Unknown Area (ID: {area_id})"
    location_name = (
        location["name"] if location else f"Unknown Location (ID: {location_id})"
    )

    # Confirm deletion
    markup = types.ReplyKeyboardMarkup(row_width=2, resize_keyboard=True)
    markup.add(
        types.KeyboardButton(f"Confirm Delete Fee {index+1}"),
        types.KeyboardButton("Cancel Delete Fee"),
        types.KeyboardButton("Back to Delivery Menu"),
        types.KeyboardButton("Back to Main Menu"),
    )

    maintenance_bot.send_message(
        user_id,
        f"Are you sure you want to delete this delivery fee?\n\nFrom: {area_name}\nTo: {location_name}\nFee: {current_fee} ETB\n\nThis action cannot be undone.",
        reply_markup=markup,
    )

    user_states[user_id] = {
        "state": "confirming_fee_delete",
        "area_id": area_id,
        "location_id": location_id,
        "index": index + 1,  # Store 1-based index for confirmation
    }


@maintenance_bot.message_handler(
    func=lambda message: message.text
    and message.text.startswith("Confirm Delete Fee ")
    and user_states.get(message.from_user.id, {}).get("state")
    == "confirming_fee_delete"
)
def confirm_delete_fee(message):
    """Confirm and process fee deletion"""
    user_id = message.from_user.id

    if not is_authorized(user_id):
        return

    area_id = user_states[user_id].get("area_id")
    location_id = user_states[user_id].get("location_id")

    if not area_id or not location_id:
        maintenance_bot.send_message(user_id, "Area ID or location ID not found.")
        show_delivery_fees_menu(message)
        return

    # Get area and location names for confirmation message
    area = get_area_by_id(area_id)
    location = get_delivery_location_by_id(location_id)

    area_name = area["name"] if area else f"Unknown Area (ID: {area_id})"
    location_name = (
        location["name"] if location else f"Unknown Location (ID: {location_id})"
    )

    # Delete the fee
    success = delete_delivery_fee(area_id, location_id)

    if success:
        # Create a keyboard with navigation options
        markup = types.ReplyKeyboardMarkup(row_width=2, resize_keyboard=True)
        markup.add(
            types.KeyboardButton("Delete Another Delivery Fee"),
            types.KeyboardButton("List Delivery Fees"),
            types.KeyboardButton("Back to Delivery Menu"),
            types.KeyboardButton("Back to Main Menu"),
        )

        maintenance_bot.send_message(
            user_id,
            f"Delivery fee deleted successfully!\nFrom: {area_name}\nTo: {location_name}",
            reply_markup=markup,
        )
    else:
        maintenance_bot.send_message(user_id, "Failed to delete delivery fee.")
        show_delivery_fees_menu(message)


@maintenance_bot.message_handler(
    func=lambda message: message.text == "Cancel Delete Fee"
    and user_states.get(message.from_user.id, {}).get("state")
    == "confirming_fee_delete"
)
def cancel_delete_fee(message):
    """Cancel fee deletion"""
    user_id = message.from_user.id

    if not is_authorized(user_id):
        return

    # Clear the state
    if user_id in user_states:
        user_states.pop(user_id)

    maintenance_bot.send_message(user_id, "Delivery fee deletion cancelled.")
    show_delivery_fees_menu(message)


@maintenance_bot.message_handler(
    func=lambda message: message.text == "Delete Another Delivery Fee"
)
def delete_another_delivery_fee(message):
    """Handle delete another delivery fee button"""
    user_id = message.from_user.id

    if not is_authorized(user_id):
        return

    # Clear any state
    if user_id in user_states:
        user_states.pop(user_id)

    # Go to delete delivery fee flow
    ask_delete_delivery_fee(message)


@maintenance_bot.message_handler(
    func=lambda message: message.text
    and message.text.startswith("Confirm Delete Location ")
    and user_states.get(message.from_user.id, {}).get("state")
    == "confirming_location_delete"
)
def confirm_delete_location(message):
    """Confirm and process location deletion with cascade delete of fees"""
    user_id = message.from_user.id

    if not is_authorized(user_id):
        return

    location_id = user_states[user_id].get("location_id")
    has_fees = user_states[user_id].get("has_fees", False)

    if not location_id:
        maintenance_bot.send_message(user_id, "Location ID not found.")
        show_delivery_menu(message)
        return

    # Get location info before deletion for confirmation message
    location = get_delivery_location_by_id(location_id)
    if not location:
        maintenance_bot.send_message(
            user_id, f"Location with ID {location_id} not found."
        )
        show_delivery_menu(message)
        return

    location_name = location["name"]

    # If there are fees, delete them first
    fees_deleted = 0
    if has_fees:
        # Get all fees for this location
        all_fees = get_all_delivery_fees()
        for fee in all_fees:
            if fee["location_id"] == location_id:
                # Delete the fee
                if delete_delivery_fee(fee["area_id"], location_id):
                    fees_deleted += 1

    # Now delete the location
    success = delete_delivery_location(location_id)

    if success:
        # Create a keyboard with navigation options
        markup = types.ReplyKeyboardMarkup(row_width=2, resize_keyboard=True)
        markup.add(
            types.KeyboardButton("View All Delivery Locations"),
            types.KeyboardButton("Back to Delivery Menu"),
            types.KeyboardButton("Back to Main Menu"),
        )

        deletion_message = f"Delivery location deleted successfully!\nID: {location_id} - {location_name}"
        if fees_deleted > 0:
            deletion_message += (
                f"\n{fees_deleted} associated delivery fees were also deleted."
            )

        maintenance_bot.send_message(
            user_id,
            deletion_message,
            reply_markup=markup,
        )
    else:
        maintenance_bot.send_message(user_id, "Failed to delete delivery location.")
        show_delivery_menu(message)


@maintenance_bot.message_handler(
    func=lambda message: message.text == "Cancel Delete"
    and user_states.get(message.from_user.id, {}).get("state")
    == "confirming_location_delete"
)
def cancel_delete_location(message):
    """Cancel location deletion"""
    user_id = message.from_user.id

    if not is_authorized(user_id):
        return

    # Get the location ID from the state
    location_id = user_states[user_id].get("location_id")

    # Clear the state
    if user_id in user_states:
        user_states.pop(user_id)

    maintenance_bot.send_message(user_id, "Delivery location deletion cancelled.")

    # If we have a location ID, go back to the location management view
    if location_id:
        show_location_management(message, location_id)
    else:
        # Otherwise go back to the locations list
        view_all_delivery_locations(message)


@maintenance_bot.message_handler(
    func=lambda message: message.text == "Delete Another Delivery Location"
)
def delete_another_location(message):
    """Handle delete another delivery location button"""
    user_id = message.from_user.id

    if not is_authorized(user_id):
        return

    # Clear any state
    if user_id in user_states:
        user_states.pop(user_id)

    # Go to view all delivery locations
    view_all_delivery_locations(message)


@maintenance_bot.message_handler(
    func=lambda message: message.text == "Manage Delivery Fees"
)
def show_delivery_fees_menu(message):
    """Show the delivery fees management menu"""
    user_id = message.from_user.id

    if not is_authorized(user_id):
        return

    markup = types.ReplyKeyboardMarkup(row_width=2, resize_keyboard=True)
    markup.add(
        types.KeyboardButton("List Delivery Fees"),
        types.KeyboardButton("Add Delivery Fee"),
        types.KeyboardButton("Update Delivery Fee"),
        types.KeyboardButton("Delete Delivery Fee"),
        types.KeyboardButton("Back to Delivery Menu"),
        types.KeyboardButton("Back to Main Menu"),
    )

    maintenance_bot.send_message(
        user_id,
        "Delivery Fees Management. What would you like to do?",
        reply_markup=markup,
    )


@maintenance_bot.message_handler(
    func=lambda message: message.text == "Add Delivery Fee"
)
def ask_delivery_fee_details(message):
    """Ask for delivery fee details"""
    user_id = message.from_user.id

    if not is_authorized(user_id):
        return

    # First, get all areas
    areas = get_all_areas()
    if not areas:
        maintenance_bot.send_message(
            user_id, "No areas found. Please add an area first."
        )
        return

    # Then, get all delivery locations
    locations = get_all_delivery_locations()
    if not locations:
        maintenance_bot.send_message(
            user_id,
            "No delivery locations found. Please add a delivery location first.",
        )
        return

    # Show areas to select from
    areas_text = "Areas:\n\n"
    for area in areas:
        areas_text += f"ID: {area['id']} - {area['name']}\n"

    areas_text += "\nEnter the ID of the area for the delivery fee:"

    markup = types.ForceReply(selective=True)
    msg = maintenance_bot.send_message(user_id, areas_text, reply_markup=markup)

    user_states[user_id] = {"state": "adding_delivery_fee_area"}
    maintenance_bot.register_next_step_handler(msg, process_delivery_fee_area)


def process_delivery_fee_area(message):
    """Process area ID for delivery fee"""
    user_id = message.from_user.id

    if not is_authorized(user_id):
        return

    try:
        area_id = int(message.text.strip())
    except ValueError:
        maintenance_bot.send_message(user_id, "Please enter a valid ID (number).")
        return

    area = get_area_by_id(area_id)
    if not area:
        maintenance_bot.send_message(user_id, f"Area with ID {area_id} not found.")
        return

    # Store the area ID and ask for delivery location
    user_states[user_id] = {"state": "adding_delivery_fee_location", "area_id": area_id}

    # Show delivery locations to select from
    locations = get_all_delivery_locations()
    locations_text = "Delivery Locations:\n\n"
    for location in locations:
        locations_text += f"ID: {location['id']} - {location['name']}\n"

    locations_text += "\nEnter the ID of the delivery location for the fee:"

    markup = types.ForceReply(selective=True)
    msg = maintenance_bot.send_message(user_id, locations_text, reply_markup=markup)

    maintenance_bot.register_next_step_handler(msg, process_delivery_fee_location)


def process_delivery_fee_location(message):
    """Process delivery location ID for fee"""
    user_id = message.from_user.id

    if not is_authorized(user_id):
        return

    try:
        location_id = int(message.text.strip())
    except ValueError:
        maintenance_bot.send_message(user_id, "Please enter a valid ID (number).")
        return

    location = get_delivery_location_by_id(location_id)
    if not location:
        maintenance_bot.send_message(
            user_id, f"Delivery location with ID {location_id} not found."
        )
        return

    area_id = user_states[user_id].get("area_id")
    if not area_id:
        maintenance_bot.send_message(user_id, "Area ID not found.")
        return

    # Store the location ID and ask for fee amount
    user_states[user_id] = {
        "state": "adding_delivery_fee_amount",
        "area_id": area_id,
        "location_id": location_id,
    }

    markup = types.ForceReply(selective=True)
    msg = maintenance_bot.send_message(
        user_id,
        f"Enter the delivery fee amount (in ETB) from {get_area_by_id(area_id)['name']} to {location['name']}:",
        reply_markup=markup,
    )

    maintenance_bot.register_next_step_handler(msg, process_delivery_fee_amount)


def process_delivery_fee_amount(message):
    """Process delivery fee amount"""
    user_id = message.from_user.id

    if not is_authorized(user_id):
        return

    try:
        # Convert fee to int instead of float
        fee_amount = int(float(message.text.strip()))
    except ValueError:
        maintenance_bot.send_message(
            user_id, "Please enter a valid fee amount (number)."
        )
        return

    area_id = user_states[user_id].get("area_id")
    location_id = user_states[user_id].get("location_id")

    if not area_id or not location_id:
        maintenance_bot.send_message(user_id, "Area ID or location ID not found.")
        return

    # Add the delivery fee
    new_fee = add_delivery_fee(area_id, location_id, fee_amount)

    if new_fee:
        area = get_area_by_id(area_id)
        location = get_delivery_location_by_id(location_id)

        area_name = area["name"] if area else f"Unknown Area ({area_id})"
        location_name = (
            location["name"] if location else f"Unknown Location ({location_id})"
        )

        # Create a keyboard with navigation options
        markup = types.ReplyKeyboardMarkup(row_width=2, resize_keyboard=True)
        markup.add(
            types.KeyboardButton("Add Another Delivery Fee"),
            types.KeyboardButton("Back to Delivery Menu"),
            types.KeyboardButton("Back to Main Menu"),
        )

        maintenance_bot.send_message(
            user_id,
            f"Delivery fee added successfully!\nFrom: {area_name}\nTo: {location_name}\nFee: {new_fee['fee']} ETB",
            reply_markup=markup,
        )
    else:
        maintenance_bot.send_message(user_id, "Failed to add delivery fee.")


@maintenance_bot.message_handler(
    func=lambda message: message.text == "Add Another Delivery Fee"
)
def add_another_delivery_fee(message):
    """Handle add another delivery fee button"""
    user_id = message.from_user.id

    if not is_authorized(user_id):
        return

    # Clear any state
    if user_id in user_states:
        user_states.pop(user_id)

    # Go to add delivery fee flow
    ask_delivery_fee_details(message)


@maintenance_bot.message_handler(commands=['firebase_diagnostics'])
def firebase_diagnostics_command(message):
    """Run Firebase diagnostics for troubleshooting deployment issues"""
    user_id = message.from_user.id

    if not is_authorized(user_id):
        return

    try:
        from src.firebase_db import diagnose_firebase_issues

        maintenance_bot.send_message(user_id, "🔍 Running Firebase diagnostics...")

        diagnostics = diagnose_firebase_issues()

        # Format diagnostics for display
        result_text = "🔧 **Firebase Diagnostics Report**\n\n"

        # Environment variables
        result_text += "📋 **Environment Variables:**\n"
        for var, status in diagnostics["environment_variables"].items():
            status_icon = "✅" if status else "❌"
            result_text += f"{status_icon} {var}: {'Set' if status else 'Not Set'}\n"

        # Credentials source
        result_text += f"\n🔑 **Credentials Source:** {diagnostics['credentials_source'] or 'None'}\n"

        # Database URL
        if diagnostics["database_url"]:
            result_text += f"🗄️ **Database URL:** {diagnostics['database_url']}\n"

        # Project ID
        if "project_id" in diagnostics:
            result_text += f"🆔 **Project ID:** {diagnostics['project_id']}\n"

        # Firebase status
        init_icon = "✅" if diagnostics["firebase_initialized"] else "❌"
        result_text += f"\n🔥 **Firebase Initialized:** {init_icon}\n"

        conn_icon = "✅" if diagnostics["connectivity_test"] else "❌"
        result_text += f"🌐 **Connectivity Test:** {conn_icon}\n"

        # Errors
        if diagnostics["errors"]:
            result_text += "\n⚠️ **Issues Found:**\n"
            for error in diagnostics["errors"]:
                result_text += f"• {error}\n"
        else:
            result_text += "\n✅ **No issues detected**\n"

        # Recommendations
        if diagnostics["errors"]:
            result_text += "\n💡 **Recommendations:**\n"
            if not diagnostics["environment_variables"]["FIREBASE_DATABASE_URL"]:
                result_text += "• Set FIREBASE_DATABASE_URL environment variable\n"
            if not diagnostics["environment_variables"]["FIREBASE_CREDENTIALS"] and not diagnostics["environment_variables"]["FIREBASE_CREDENTIALS_PATH"]:
                result_text += "• Set FIREBASE_CREDENTIALS environment variable with your service account JSON\n"
            if not diagnostics["firebase_initialized"]:
                result_text += "• Check Firebase credentials and database URL\n"
            if not diagnostics["connectivity_test"]:
                result_text += "• Verify network connectivity and Firebase permissions\n"

        maintenance_bot.send_message(user_id, result_text, parse_mode='Markdown')

    except Exception as e:
        logger.error(f"Error running Firebase diagnostics: {e}", exc_info=True)
        maintenance_bot.send_message(
            user_id,
            f"❌ Error running diagnostics: {str(e)}\n\nPlease check the logs for more details."
        )


def register_handlers():
    """Register all handlers in this module"""
    # All handlers are already registered using decorators
    pass
