name: Sync Data from Railway

on:
  schedule:
    # Run every 6 hours
    - cron: '0 */6 * * *'
  # Allow manual triggering
  workflow_dispatch:

jobs:
  sync-data:
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
      
      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.9'
      
      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          pip install requests python-telegram-bot
      
      - name: Fetch data from Telegram bot
        env:
          BOT_TOKEN: ${{ secrets.MAINTENANCE_BOT_TOKEN }}
          CHAT_ID: ${{ secrets.MAINTENANCE_CHAT_ID }}
        run: |
          python .github/scripts/fetch_data.py
      
      - name: Commit and push changes
        run: |
          git config --global user.name 'GitHub Action'
          git config --global user.email '<EMAIL>'
          
          # Check if there are any changes
          if [[ -n $(git status -s) ]]; then
            git add data_files/*.json
            git commit -m "Auto-sync data from Railway deployment"
            git push
          else
            echo "No changes to commit"
          fi
