# Git
.git
.gitignore
.gitattributes

# Documentation
*.md
docs/
LICENSE

# Python
__pycache__
*.pyc
*.pyo
*.pyd
.Python
env
pip-log.txt
pip-delete-this-directory.txt
.tox
.coverage
.coverage.*
.pytest_cache
htmlcov

# Virtual environments
venv/
env/
ENV/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
*.log
logs/

# Environment files (will be provided via docker-compose)
.env
.env.local
.env.*.local

# Build artifacts
build/
dist/
*.egg-info/

# Temporary files
tmp/
temp/ 